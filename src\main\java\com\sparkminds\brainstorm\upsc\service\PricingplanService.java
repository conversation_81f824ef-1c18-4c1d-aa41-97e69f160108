package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;

import com.sparkminds.brainstorm.upsc.model.Pricingplan;
import com.sparkminds.brainstorm.upsc.repository.PricingplanRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class PricingplanService {
    private final PricingplanRepository repository;
    public PricingplanService(PricingplanRepository repository) { this.repository = repository; }
    public Flux<Pricingplan> findAll() { return repository.findAll(); }
    public Mono<Pricingplan> findById(String id) { return repository.findById(id); }
    public Mono<Pricingplan> save(Pricingplan obj) { return repository.save(obj); }
    public Mono<Void> delete(String id) { repository.deleteById(id);
        return null;
    }
}