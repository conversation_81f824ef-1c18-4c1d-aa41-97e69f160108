package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.DownloadLimit;
import com.sparkminds.brainstorm.upsc.repository.DownloadLimitRepository;

@Service
public class DownloadLimitService {
    private final DownloadLimitRepository repository;
    
    public DownloadLimitService(DownloadLimitRepository repository) { 
        this.repository = repository; 
    }
    
    public Flux<DownloadLimit> findAll() { 
        return repository.findAll(); 
    }
    
    public Mono<DownloadLimit> findById(String id) { 
        return repository.findById(id); 
    }
    
    public Mono<DownloadLimit> save(DownloadLimit obj) { 
        return repository.save(obj); 
    }
    
    public Mono<Void> delete(String id) { 
        return repository.deleteById(id); 
    }
}