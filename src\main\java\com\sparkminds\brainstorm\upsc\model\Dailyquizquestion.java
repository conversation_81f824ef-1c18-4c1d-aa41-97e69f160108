package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.*;

import static com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil.convertToLocalDateTime;

@Document(collectionName = "daily_quiz_questions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Dailyquizquestion {
    @DocumentId
    private String id;
    private Object date;
    private String question;
    private List<String> options;
    private Integer correctAnswer;
    private String explanation;
    private String subject;

    public LocalDateTime getDateAsLocalDateTime() {
        return convertToLocalDateTime(date);
    }
}