package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.model.Userdailyquizprogre;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import com.sparkminds.brainstorm.upsc.service.UserdailyquizprogreService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/userDailyQuizProgress")
public class UserdailyquizprogreController {
    private final UserdailyquizprogreService service;

    public UserdailyquizprogreController(UserdailyquizprogreService service) {
        this.service = service;
    }

    // Get progress by user ID (most useful endpoint for the frontend)
    @GetMapping("/by-user")
    public Mono<ResponseEntity<Userdailyquizprogre>> getByUserId() {
        log.info("Fetching daily quiz progress for authenticated user");
        return SecurityUtils.getCurrentUserFirebaseUid()
                .flatMap(userId -> {
                    log.info("Fetching quiz progress for Firebase UID: {}", userId);
                    return service.findByUserId(userId)
                            .map(ResponseEntity::ok)
                            .switchIfEmpty(Mono.just(ResponseEntity.notFound().build()));
                })
                .switchIfEmpty(Mono.just(ResponseEntity.status(401).body(null)))
                .onErrorResume(e -> {
                    log.error("Error fetching quiz progress: {}", e.getMessage());
                    return Mono.just(ResponseEntity.status(500).body(null));
                });
    }

    @GetMapping
    public Flux<Userdailyquizprogre> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<Userdailyquizprogre> getById(@PathVariable String id) {
        return service.findById(id);
    }

    // Other endpoints for administrative purposes if needed
    @PostMapping
    public Mono<Userdailyquizprogre> create(@RequestBody Userdailyquizprogre obj) {
        return service.save(obj);
    }

    @PutMapping("/{id}")
    public Mono<Userdailyquizprogre> update(@PathVariable String id, @RequestBody Userdailyquizprogre obj) {
        obj.setId(id);
        return service.save(obj);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }
}