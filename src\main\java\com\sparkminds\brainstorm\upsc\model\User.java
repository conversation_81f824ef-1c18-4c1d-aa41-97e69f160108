package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import jakarta.xml.bind.DatatypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import com.sparkminds.brainstorm.upsc.util.FirestoreDateDeserializer;

@Document(collectionName = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {
    @DocumentId
    private String id;
    private String firebaseUid;
    private String email;
    private String role;
    private boolean active = true;
    private Object createdAt;
    private Object lastLoginAt;
    // Remove @OneToOne - handle relationship differently in Firestore

    public Date getCreatedAtAsDate() {
        return convertToDate(createdAt);
    }

    public Date getLastLoginAtAsDate() {
        return convertToDate(lastLoginAt);
    }

    private Date convertToDate(Object obj) {
        if (obj == null) return null;
        if (obj instanceof Date) return (Date) obj;
        if (obj instanceof Long) return new Date((Long) obj);
        if (obj instanceof String) {
            try {
                return java.sql.Timestamp.valueOf((String) obj);
            } catch (Exception e) {
                // fallback to ISO8601
                try {
                    return DatatypeConverter.parseDateTime((String) obj).getTime();
                } catch (Exception ex) {
                    return null;
                }
            }
        }
        if (obj instanceof java.util.Map) {
            java.util.Map map = (java.util.Map) obj;
            // Firestore LocalDateTime map
            if (map.containsKey("year") && map.containsKey("monthValue") && map.containsKey("dayOfMonth")) {
                int year = (int) map.get("year");
                int month = (int) map.get("monthValue");
                int day = (int) map.get("dayOfMonth");
                int hour = map.containsKey("hour") ? (int) map.get("hour") : 0;
                int minute = map.containsKey("minute") ? (int) map.get("minute") : 0;
                int second = map.containsKey("second") ? (int) map.get("second") : 0;
                int nano = map.containsKey("nano") ? (int) map.get("nano") : 0;
                java.time.LocalDateTime ldt = java.time.LocalDateTime.of(year, month, day, hour, minute, second, nano);
                return java.util.Date.from(ldt.atZone(java.time.ZoneId.systemDefault()).toInstant());
            }
            // Firestore Timestamp map
            if (map.containsKey("_seconds")) {
                long seconds = ((Number) map.get("_seconds")).longValue();
                long nanos = map.containsKey("_nanoseconds") ? ((Number) map.get("_nanoseconds")).longValue() : 0L;
                return new Date(seconds * 1000 + nanos / 1000000);
            }
        }
        return null;
    }
}
