package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Document(collectionName = "notifications")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Notification {
    @DocumentId
    private String id;
    private String title;
    private String message;
    private String type;
    private String timestamp;
    private Object read;
    private String audience;
    private String status;
    private Object sentAt;
    private Integer recipients;
    private Integer openRate;

    public LocalDateTime getSentAtAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(sentAt);
    }

    public Integer getReadAsInteger() {
        if (read == null) return null;
        if (read instanceof Integer) return (Integer) read;
        if (read instanceof Boolean) return (Boolean) read ? 1 : 0;
        if (read instanceof String) {
            try {
                return Integer.parseInt((String) read);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }
}