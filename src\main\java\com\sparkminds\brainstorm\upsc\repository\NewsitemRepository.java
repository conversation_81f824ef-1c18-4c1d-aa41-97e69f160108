package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.Newsitem;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface NewsitemRepository extends FirestoreReactiveRepository<Newsitem> {
    Flux<Newsitem> findByCategory(String category);

    Flux<Newsitem> findByImportance(String importance);
}