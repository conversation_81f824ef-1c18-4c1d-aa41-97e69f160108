package com.sparkminds.brainstorm.upsc.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.config.EnableWebFlux;
import org.springframework.web.reactive.config.WebFluxConfigurer;

@Configuration
@EnableWebFlux
public class WebFluxConfig implements WebFluxConfigurer {
    // No custom codec configuration needed unless you're customizing JSON, Protobuf, etc.
    // Multipart is already enabled by default in Spring Boot 3+ (Spring Framework 6+)
}
