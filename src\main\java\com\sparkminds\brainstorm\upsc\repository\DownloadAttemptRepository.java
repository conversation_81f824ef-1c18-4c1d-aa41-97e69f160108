package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.DownloadAttempt;
import reactor.core.publisher.Flux;

public interface DownloadAttemptRepository extends FirestoreReactiveRepository<DownloadAttempt> {
    Flux<DownloadAttempt> findByUserId(String userId);
    Flux<DownloadAttempt> findByMaterialId(String materialId);
    Flux<DownloadAttempt> findByStatus(String status);
}
