package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Testimonial;
import com.sparkminds.brainstorm.upsc.service.TestimonialService;

@RestController
@RequestMapping("/api/testimonials")
public class TestimonialController {
    private final TestimonialService service;
    
    public TestimonialController(TestimonialService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<Testimonial> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<Testimonial> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<Testimonial> create(@RequestBody Testimonial obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<Testimonial> update(@PathVariable String id, @RequestBody Testimonial obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}