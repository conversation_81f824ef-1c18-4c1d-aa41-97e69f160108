package com.sparkminds.brainstorm.upsc.repository;

import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.Firestore;
import com.google.cloud.firestore.Query;
import com.google.cloud.firestore.QueryDocumentSnapshot;
import com.google.cloud.firestore.QuerySnapshot;
import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.Question;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

public interface QuestionRepository extends FirestoreReactiveRepository<Question> {
    Flux<Question> findBySubject(String subject);
    Flux<Question> findByCategory(String category);
    Flux<Question> findByDifficulty(String difficulty);
    Flux<Question> findByStatus(String status);
    Flux<Question> findByQuestionType(String questionType);
    Flux<Question> findByExamId(String examId);

    // MODIFICATION: Replace the old findRandomQuestionIds with this new one.
    default Flux<String> findRandomQuestionIds(int limit, Firestore firestore) {
        return Flux.create(sink -> {
            // Step 1: Talk directly to the 'questions' collection in the database.
            ApiFuture<QuerySnapshot> future = firestore.collection("questions").get();

            // Step 2: When the database responds, run this code.
            future.addListener(() -> {
                try {
                    // Get all the documents.
                    List<QueryDocumentSnapshot> documents = future.get().getDocuments();

                    // Get a simple list of all the document ID strings.
                    List<String> allIds = documents.stream()
                            .map(QueryDocumentSnapshot::getId)
                            .collect(Collectors.toList());

                    // Check if we have enough questions.
                    if (allIds.size() < limit) {
                        sink.error(new RuntimeException("Not enough questions in database. Required: " + limit + ", Found: " + allIds.size()));
                        return;
                    }

                    // Step 3: Shuffle the list of IDs randomly.
                    Collections.shuffle(allIds);

                    // Step 4: Take the first 'limit' (10) questions from the shuffled list.
                    List<String> randomIds = allIds.subList(0, limit);

                    // Step 5: Send the 10 random IDs back to the service.
                    randomIds.forEach(sink::next);
                    sink.complete();

                } catch (Exception e) {
                    // If anything goes wrong, send an error.
                    sink.error(e);
                }
            }, Executors.newSingleThreadExecutor());
        });
    }
}