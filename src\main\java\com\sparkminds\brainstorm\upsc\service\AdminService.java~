package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@Service
public class AdminService {
    
    private final AdminstatService adminstatService;
    private final UserProfileService userProfileService;
    private final StudymaterialService studymaterialService;
    private final ExamService examService;
    private final PurchaseService purchaseService;
    private final ProgressdataService progressdataService;
    private final RecentmaterialService recentmaterialService;
    
    public AdminService(AdminstatService adminstatService, 
                       UserProfileService userProfileService,
                       StudymaterialService studymaterialService,
                       ExamService examService,
                       PurchaseService purchaseService,
                       ProgressdataService progressdataService,
                       RecentmaterialService recentmaterialService) {
        this.adminstatService = adminstatService;
        this.userProfileService = userProfileService;
        this.studymaterialService = studymaterialService;
        this.examService = examService;
        this.purchaseService = purchaseService;
        this.progressdataService = progressdataService;
        this.recentmaterialService = recentmaterialService;
    }
    
    public Mono<Map<String, Object>> getAdminStats() {
        return Mono.zip(
            userProfileService.findAll().count(),
            studymaterialService.findAll().count(),
            examService.findAll().count(),
            purchaseService.findAll().count()
        ).map(tuple -> {
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalUsers", tuple.getT1());
            stats.put("totalMaterials", tuple.getT2());
            stats.put("totalExams", tuple.getT3());
            stats.put("totalPurchases", tuple.getT4());
            return stats;
        });
    }
    
    public Flux<Map<String, Object>> getProgressData() {
        return progressdataService.findAll()
            .map(progress -> {
                Map<String, Object> data = new HashMap<>();
                data.put("id", progress.getId());
                data.put("userId", progress.getUserId());
                data.put("progressMonth", progress.getProgressMonth());
                data.put("score", progress.getScore());
                data.put("accuracy", progress.getAccuracy());
                data.put("createdAt", progress.getCreatedAt());
                return data;
            });
    }
    
    public Flux<Map<String, Object>> getRecentMaterials() {
        return recentmaterialService.findAll()
            .map(material -> {
                Map<String, Object> data = new HashMap<>();
                data.put("id", material.getId());
                data.put("title", material.getTitle());
                data.put("type", material.getType());
                //data.put("accessedAt", material.ge);
                return data;
            });
    }
}