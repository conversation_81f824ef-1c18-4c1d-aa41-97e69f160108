/*
package com.sparkminds.brainstorm.upsc.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.sparkminds.brainstorm.upsc.model.*;
import com.sparkminds.brainstorm.upsc.service.*;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;

@Component
public class DataLoader {

    private static final Logger logger = LoggerFactory.getLogger(DataLoader.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired private UserProfileService userProfileService;
    @Autowired private StudymaterialService studyMaterialService;
    @Autowired private ExamService examService;
    @Autowired private QuestionService questionService;
    @Autowired private DailyquizquestionService dailyQuizQuestionService;
    @Autowired private TestimonialService testimonialService;
    @Autowired private PricingplanService pricingPlanService;
    @Autowired private PurchaseService purchaseService;
    @Autowired private NotificationService notificationService;
    @Autowired private AdminstatService adminStatService;
    @Autowired private UsergrowthdataService userGrowthDataService;
    @Autowired private ProgressdataService progressDataService;
    @Autowired private UpcomingexamService upcomingExamService;
    @Autowired private RecentmaterialService recentMaterialService;
    @Autowired private UserdailyquizprogreService userDailyQuizProgressService;
    @Autowired private DailyquizsubmissionService dailyQuizSubmissionService;
    @Autowired private DownloadAttemptService downloadAttemptService;
    @Autowired private DownloadLimitService downloadLimitService;

    @PostConstruct
    public void loadData() {
        try {
            logger.info("Starting data loading from db.json...");
            
            // Configure ObjectMapper for Java 17+ compatibility
            objectMapper.registerModule(new JavaTimeModule());
            objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
            
            ClassPathResource resource = new ClassPathResource("db.json");
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            
            // Load data in order (considering dependencies)
            loadUserProfiles(rootNode.get("userProfiles"));
            loadStudyMaterials(rootNode.get("studyMaterials"));
            loadExams(rootNode.get("exams"));
            loadQuestions(rootNode.get("questions"));
            loadDailyQuizQuestions(rootNode.get("dailyQuizQuestions"));
            loadTestimonials(rootNode.get("testimonials"));
            loadPricingPlans(rootNode.get("pricingPlans"));
            loadPurchases(rootNode.get("purchases"));
            loadNotifications(rootNode.get("notifications"));
            loadAdminStats(rootNode.get("adminStats"));
            loadUserGrowthData(rootNode.get("userGrowthData"));
            loadProgressData(rootNode.get("progressData"));
            loadUpcomingExams(rootNode.get("upcomingExams"));
            loadRecentMaterials(rootNode.get("recentMaterials"));
            loadUserDailyQuizProgress(rootNode.get("userDailyQuizProgress"));
            loadDailyQuizSubmissions(rootNode.get("dailyQuizSubmissions"));
            loadDownloadAttempts(rootNode.get("downloadAttempts"));
            loadDownloadLimits(rootNode.get("downloadLimits"));
            
            logger.info("Data loading completed successfully!");
            
        } catch (IOException e) {
            logger.error("Error loading data from db.json: ", e);
        }
    }

    private void loadUserProfiles(JsonNode node) {
        if (node != null && node.isArray()) {
            logger.info("Loading user profiles...");
            for (JsonNode profileNode : node) {
                try {
                    UserProfile profile = new UserProfile();
                    
                    // Add null checks for all fields
                    if (profileNode.has("id") && !profileNode.get("id").isNull()) {
                        profile.setId(profileNode.get("id").asText());
                    }
                    if (profileNode.has("userId") && !profileNode.get("userId").isNull()) {
                        profile.setUserId(profileNode.get("userId").asText());
                    }
                    if (profileNode.has("fullName") && !profileNode.get("fullName").isNull()) {
                        profile.setName(profileNode.get("fullName").asText());
                    }
                    if (profileNode.has("email") && !profileNode.get("email").isNull()) {
                        profile.setEmail(profileNode.get("email").asText());
                    }
                    if (profileNode.has("mobile") && !profileNode.get("mobile").isNull()) {
                        profile.setPhone(profileNode.get("mobile").asText());
                    }
                    if (profileNode.has("dateOfBirth") && !profileNode.get("dateOfBirth").isNull()) {
                        profile.setDateOfBirth(profileNode.get("dateOfBirth").asText());
                    }
                    if (profileNode.has("address") && !profileNode.get("address").isNull()) {
                        profile.setAddress(profileNode.get("address").asText());
                    }
                    if (profileNode.has("city") && !profileNode.get("city").isNull()) {
                        profile.setCity(profileNode.get("city").asText());
                    }
                    if (profileNode.has("state") && !profileNode.get("state").isNull()) {
                        profile.setState(profileNode.get("state").asText());
                    }
                    if (profileNode.has("pincode") && !profileNode.get("pincode").isNull()) {
                        profile.setPincode(profileNode.get("pincode").asText());
                    }
                    if (profileNode.has("avatar") && !profileNode.get("avatar").isNull()) {
                        profile.setAvatar(profileNode.get("avatar").asText());
                    }
                    if (profileNode.has("createdAt") && !profileNode.get("createdAt").isNull()) {
                        profile.setCreatedAt(parseDateTime(profileNode.get("createdAt").asText()));
                    }
                    if (profileNode.has("updatedAt") && !profileNode.get("updatedAt").isNull()) {
                        profile.setUpdatedAt(parseDateTime(profileNode.get("updatedAt").asText()));
                    }
                    
                    userProfileService.save(profile).subscribe();
                } catch (Exception e) {
                    logger.error("Error loading user profile: ", e);
                }
            }
        }
    }

    private void loadStudyMaterials(JsonNode node) {
        if (node != null && node.isArray()) {
            logger.info("Loading study materials...");
            for (JsonNode materialNode : node) {
                try {
                    Studymaterial material = new Studymaterial();
                    
                    // Add null checks for all fields
                    if (materialNode.has("id") && materialNode.get("id") != null) {
                        material.setId(materialNode.get("id").asText());
                    }
                    if (materialNode.has("title") && materialNode.get("title") != null) {
                        material.setTitle(materialNode.get("title").asText());
                    }
                    if (materialNode.has("description") && materialNode.get("description") != null) {
                        material.setDescription(materialNode.get("description").asText());
                    }
                    if (materialNode.has("type") && materialNode.get("type") != null) {
                        material.setType(materialNode.get("type").asText());
                    }
                    if (materialNode.has("subject") && materialNode.get("subject") != null) {
                        material.setSubject(materialNode.get("subject").asText());
                    }
                    if (materialNode.has("price") && materialNode.get("price") != null) {
                        material.setPrice(materialNode.get("price").asInt());
                    }
                    if (materialNode.has("originalPrice") && materialNode.get("originalPrice") != null) {
                        material.setOriginalPrice(materialNode.get("originalPrice").asInt());
                    }
                    if (materialNode.has("rating") && materialNode.get("rating") != null) {
                        material.setRating(materialNode.get("rating").asDouble());
                    }
                    if (materialNode.has("reviews") && materialNode.get("reviews") != null) {
                        material.setReviews(materialNode.get("reviews").asInt());
                    }
                    if (materialNode.has("author") && materialNode.get("author") != null) {
                        material.setAuthor(materialNode.get("author").asText());
                    }
                    if (materialNode.has("isPremium") && materialNode.get("isPremium") != null) {
                        material.setIsPremium(materialNode.get("isPremium").asBoolean() ? 1 : 0);
                    }
                    if (materialNode.has("thumbnailUrl") && materialNode.get("thumbnailUrl") != null) {
                        material.setThumbnailUrl(materialNode.get("thumbnailUrl").asText());
                    }
                    if (materialNode.has("status") && materialNode.get("status") != null) {
                        material.setStatus(materialNode.get("status").asText());
                    }
                    if (materialNode.has("downloads") && materialNode.get("downloads") != null) {
                        material.setDownloads(materialNode.get("downloads").asInt());
                    }
                    if (materialNode.has("createdAt") && materialNode.get("createdAt") != null) {
                        String dateStr = materialNode.get("createdAt").asText();
                        material.setCreatedAt(LocalDate.parse(dateStr).atStartOfDay());
                    }
                    if (materialNode.has("fileSize") && materialNode.get("fileSize") != null) {
                        material.setFileSize(materialNode.get("fileSize").asText());
                    }
                    
                    studyMaterialService.save(material).subscribe();
                } catch (Exception e) {
                    logger.error("Error loading study material: ", e);
                }
            }
        }
    }

    private void loadTestimonials(JsonNode node) {
        if (node != null && node.isArray()) {
            logger.info("Loading testimonials...");
            for (JsonNode testimonialNode : node) {
                try {
                    Testimonial testimonial = new Testimonial();
                    testimonial.setId(testimonialNode.get("id").asText());
                    testimonial.setName(testimonialNode.get("name").asText());
                    testimonial.setRank(testimonialNode.get("rank").asText());
                    testimonial.setImage(testimonialNode.get("image").asText());
                    testimonial.setQuote(testimonialNode.get("quote").asText());
                    testimonial.setFeatured(testimonialNode.get("featured").asInt() == 1 ? 1 : 0);
                    
                    testimonialService.save(testimonial).subscribe();
                } catch (Exception e) {
                    logger.error("Error loading testimonial: ", e);
                }
            }
        }
    }

    private void loadUserGrowthData(JsonNode node) {
        if (node != null && node.isArray()) {
            logger.info("Loading user growth data...");
            for (JsonNode growthNode : node) {
                try {
                    Usergrowthdata growth = new Usergrowthdata();
                    growth.setId(growthNode.get("id").asText());
                    growth.setGrowthMonth(growthNode.get("month").asText());
                    growth.setUsers(growthNode.get("users").asInt());
                    
                    userGrowthDataService.save(growth).subscribe();
                } catch (Exception e) {
                    logger.error("Error loading user growth data: ", e);
                }
            }
        }
    }

    private void loadProgressData(JsonNode node) {
        if (node != null && node.isArray()) {
            logger.info("Loading progress data...");
            for (JsonNode progressNode : node) {
                try {
                    Progressdata progress = new Progressdata();
                    progress.setId(progressNode.get("id").asText());
                    progress.setProgressMonth(progressNode.get("month").asText());
                    progress.setScore(progressNode.get("score").asInt());
                    
                    progressDataService.save(progress).subscribe();
                } catch (Exception e) {
                    logger.error("Error loading progress data: ", e);
                }
            }
        }
    }

    private void loadDownloadAttempts(JsonNode node) {
        if (node != null && node.isArray()) {
            logger.info("Loading download attempts...");
            for (JsonNode attemptNode : node) {
                try {
                    DownloadAttempt attempt = new DownloadAttempt();
                    attempt.setId(attemptNode.get("id").asText());
                    attempt.setUserId(attemptNode.get("userId").asText());
                    attempt.setMaterialId(attemptNode.get("materialId").asText());
                    attempt.setDownloadId(attemptNode.get("downloadId").asText());
                    attempt.setAttemptedAt(parseDateTime(attemptNode.get("attemptedAt").asText()));
                    if (attemptNode.has("completedAt") && !attemptNode.get("completedAt").isNull()) {
                        attempt.setCompletedAt(parseDateTime(attemptNode.get("completedAt").asText()));
                    }
                    attempt.setStatus(attemptNode.get("status").asText());
                    attempt.setIpAddress(attemptNode.get("ipAddress").asText());
                    attempt.setUserAgent(attemptNode.get("userAgent").asText());
                    attempt.setProgress(attemptNode.get("progress").asInt());
                    attempt.setFileSize(attemptNode.get("fileSize").asText());
                    attempt.setDownloadDuration(attemptNode.get("downloadDuration").asInt());
                    
                    downloadAttemptService.save(attempt).subscribe();
                } catch (Exception e) {
                    logger.error("Error loading download attempt: ", e);
                }
            }
        }
    }

    private void loadDownloadLimits(JsonNode node) {
        if (node != null && node.isArray()) {
            logger.info("Loading download limits...");
            for (JsonNode limitNode : node) {
                try {
                    DownloadLimit limit = new DownloadLimit();
                    limit.setUserId(limitNode.get("userId").asText());
                    limit.setMaterialId(limitNode.get("materialId").asText());
                    limit.setMaxDownloads(limitNode.get("maxDownloads").asInt());
                    limit.setUsedDownloads(limitNode.get("usedDownloads").asInt());
                    if (limitNode.has("lastDownloadAt") && !limitNode.get("lastDownloadAt").isNull()) {
                        limit.setLastDownloadAt(parseDateTime(limitNode.get("lastDownloadAt").asText()));
                    }
                    limit.setResetDate(parseDateTime(limitNode.get("resetDate").asText()));
                    
                    downloadLimitService.save(limit).subscribe();
                } catch (Exception e) {
                    logger.error("Error loading download limit: ", e);
                }
            }
        }
    }

    // Add placeholder methods for other entities
    private void loadExams(JsonNode node) { */
/* TODO: Implement when Exam model is available *//*
 }
    private void loadQuestions(JsonNode node) { */
/* TODO: Implement when Question model is available *//*
 }
    private void loadDailyQuizQuestions(JsonNode node) { */
/* TODO: Implement when DailyQuizQuestion model is available *//*
 }
    private void loadPricingPlans(JsonNode node) { */
/* TODO: Implement when PricingPlan model is available *//*
 }
    private void loadPurchases(JsonNode node) { */
/* TODO: Implement when Purchase model is available *//*
 }
    private void loadNotifications(JsonNode node) { */
/* TODO: Implement when Notification model is available *//*
 }
    private void loadAdminStats(JsonNode node) { */
/* TODO: Implement when AdminStat model is available *//*
 }
    private void loadUpcomingExams(JsonNode node) { */
/* TODO: Implement when UpcomingExam model is available *//*
 }
    private void loadRecentMaterials(JsonNode node) { */
/* TODO: Implement when RecentMaterial model is available *//*
 }
    private void loadUserDailyQuizProgress(JsonNode node) { */
/* TODO: Implement when UserDailyQuizProgress model is available *//*
 }
    private void loadDailyQuizSubmissions(JsonNode node) { */
/* TODO: Implement when DailyQuizSubmission model is available *//*
 }

    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty() || "null".equals(dateTimeStr)) {
            return null; // Return null instead of current time for optional fields
        }
        try {
            return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ISO_DATE_TIME);
        } catch (Exception e) {
            logger.warn("Failed to parse datetime: " + dateTimeStr + ", using current time");
            return LocalDateTime.now();
        }
    }
}*/
