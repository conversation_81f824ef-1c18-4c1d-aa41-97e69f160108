package com.sparkminds.brainstorm.upsc.security;


import com.sparkminds.brainstorm.upsc.model.User;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;

public class UserDetail implements UserDetails {

    private final User user;

    public UserDetail(User user) {
        this.user = user;
    }

    public User getUser() {
        return user;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // Convert the student's role to a GrantedAuthority.
        // Spring Security’s hasRole() checks for "ROLE_" prefixed authorities.
        if (user.getRole() != null) {
            return Collections.singletonList(
                    new SimpleGrantedAuthority("ROLE_" + user.getRole().toUpperCase())
            );
        }
        return Collections.emptyList();
    }

    @Override
    public String getPassword() {
        // Since authentication is handled by Firebase, you may not have a password.
        return "";
    }

    @Override
    public String getUsername() {
        // Use a unique identifier such as the Firebase UID (or email) from your Student record.
        return user.getFirebaseUid();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        // You can include any logic here to check if the student's account is active.
        return true;
    }
}
