package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.model.Exam;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import com.sparkminds.brainstorm.upsc.service.ExamService;
import com.sparkminds.brainstorm.upsc.service.ExamResultService;
import com.sparkminds.brainstorm.upsc.service.PurchaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/exams")
public class ExamController {
    private static final Logger log = LoggerFactory.getLogger(ExamController.class);
    private final ExamService service;
    private final ExamResultService examResultService;
    private final PurchaseService purchaseService; // Add PurchaseService dependency

    public ExamController(ExamService examService, ExamResultService examResultService, PurchaseService purchaseService) {
        this.service = examService;
        this.examResultService = examResultService;
        this.purchaseService = purchaseService;
    }

    @GetMapping
    public Flux<Exam> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<ResponseEntity<Exam>> getById(@PathVariable String id) {
        return service.findById(id)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    @GetMapping("/today")
    public Flux<Exam> getTodayExams() {
        return service.findTodayExams();
    }

    @GetMapping("/this-week")
    public Flux<Exam> getThisWeekExams() {
        return service.findThisWeekExams();
    }

    @GetMapping("/status/{status}")
    public Flux<Exam> getByStatus(@PathVariable String status) {
        return service.findByStatus(status);
    }

    @GetMapping("/category/{category}")
    public Flux<Exam> getByCategory(@PathVariable String category) {
        return service.findByCategory(category);
    }

    @GetMapping("/premium/{isPremium}")
    public Flux<Exam> getByIsPremium(@PathVariable Integer isPremium) {
        return service.findByIsPremium(isPremium);
    }

    @PostMapping
    public Mono<ResponseEntity<Exam>> create(@RequestBody Exam exam) {
        return service.save(exam)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.badRequest().build());
    }

    @PutMapping("/{id}")
    public Mono<ResponseEntity<Exam>> update(@PathVariable String id, @RequestBody Exam exam) {
        exam.setId(id);
        return service.save(exam)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.badRequest().build());
    }

    @GetMapping("/me")
    public Mono<ResponseEntity<Flux<Exam>>> getCurrentUserExams(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.warn("Unauthorized access to /api/exams/me (no authentication)");
            return Mono.just(ResponseEntity.status(401).build());
        }

        return SecurityUtils.getCurrentUserFirebaseUid()
                .switchIfEmpty(Mono.error(new IllegalStateException("No authenticated user found")))
                .flatMap(userId -> {
                    log.info("Fetching premium exams for authenticated userId: {}", userId);
                    return purchaseService.findByUserId(userId)
                            .filter(purchase -> "completed".equals(purchase.getStatus()) && Boolean.TRUE.equals(purchase.getAccessGranted()))
                            .flatMap(purchase -> service.findById(purchase.getMaterialId())
                                    .filter(exam -> exam.getIsPremium() == 1) // Only include premium exams
                                    .doOnNext(exam -> log.debug("Found premium exam for user {}: {}", userId, exam.getId()))
                                    .onErrorResume(e -> {
                                        log.warn("Exam not found for materialId {}: {}", purchase.getMaterialId(), e.getMessage());
                                        return Mono.empty();
                                    }))
                            .collectList()
                            .map(exams -> ResponseEntity.ok(Flux.fromIterable(exams)))
                            .defaultIfEmpty(ResponseEntity.ok(Flux.empty()));
                })
                .doOnError(e -> log.error("Error fetching premium exams for user: {}", e.getMessage()))
                .onErrorResume(e -> Mono.just(ResponseEntity.status(401).build()));
    }

    @GetMapping("/upcoming")
    public Flux<Exam> getUpcomingExams() {
        return service.findUpcomingExams();
    }

    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Object>> delete(@PathVariable String id) {
        return service.delete(id)
                .then(Mono.just(ResponseEntity.ok().build()))
                .onErrorResume(e -> Mono.just(ResponseEntity.badRequest().build()));
    }

    @PostMapping("/{id}/start")
    public Mono<ResponseEntity<Exam>> startExam(@PathVariable String id) {
        return service.startExam(id)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.badRequest().build())
                .onErrorResume(e -> Mono.just(ResponseEntity.status(403).body(null)));
    }

    @GetMapping("/{id}/access")
    public Mono<ResponseEntity<Boolean>> checkAccess(@PathVariable String id) {
        return service.canUserAccessExam(null, id) // User ID will be obtained from SecurityUtils in service
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.ok(false));
    }
}