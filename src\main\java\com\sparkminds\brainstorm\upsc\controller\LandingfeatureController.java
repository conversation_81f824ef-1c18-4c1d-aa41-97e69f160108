package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Landingfeature;
import com.sparkminds.brainstorm.upsc.service.LandingfeatureService;

@RestController
@RequestMapping("/api/landingFeatures")
public class LandingfeatureController {
    private final LandingfeatureService service;

    public LandingfeatureController(LandingfeatureService service) {
        this.service = service;
    }

    @GetMapping
    public Flux<Landingfeature> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<Landingfeature> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @PostMapping
    public Mono<Landingfeature> create(@RequestBody Landingfeature obj) {
        return service.save(obj);
    }

    @PutMapping("/{id}")
    public Mono<Landingfeature> update(@PathVariable String id, @RequestBody Landingfeature obj) {
        obj.setId(id);
        return service.save(obj);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }
}