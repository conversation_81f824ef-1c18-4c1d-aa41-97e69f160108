package com.sparkminds.brainstorm.upsc.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RazorpayPaymentData {
    private String id;
    private String entity;
    private Long amount;
    private String currency;
    private String status;
    private String orderId;
    private String method;
    private String email;
    private String contact;
    private Long createdAt;
}
