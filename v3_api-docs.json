{"openapi": "3.0.1", "info": {"title": "Spring Boot Backend API", "description": "API for Spring Boot 3.1.0 with Firestore, H2, and Firebase Authentication", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8081", "description": "Generated server url"}], "security": [{"bearerAuth": []}], "paths": {"/api/users/{id}": {"get": {"tags": ["user-controller"], "operationId": "getById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "put": {"tags": ["user-controller"], "operationId": "update", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "delete": {"tags": ["user-controller"], "operationId": "delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/userProfiles/{id}": {"get": {"tags": ["user-profile-controller"], "operationId": "getById_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}}}}, "put": {"tags": ["user-profile-controller"], "operationId": "update_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}}}}, "delete": {"tags": ["user-profile-controller"], "operationId": "delete_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/userGrowthData/{id}": {"get": {"tags": ["usergrowthdata-controller"], "operationId": "getById_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Usergrowthdata"}}}}}}, "put": {"tags": ["usergrowthdata-controller"], "operationId": "update_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Usergrowthdata"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Usergrowthdata"}}}}}}, "delete": {"tags": ["usergrowthdata-controller"], "operationId": "delete_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/userDailyQuizProgress/{id}": {"get": {"tags": ["userdailyquizprogre-controller"], "operationId": "getById_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Userdailyquizprogre"}}}}}}, "put": {"tags": ["userdailyquizprogre-controller"], "operationId": "update_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Userdailyquizprogre"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Userdailyquizprogre"}}}}}}, "delete": {"tags": ["userdailyquizprogre-controller"], "operationId": "delete_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/upcomingExams/{id}": {"get": {"tags": ["upcomingexam-controller"], "operationId": "getById_4", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Upcomingexam"}}}}}}, "put": {"tags": ["upcomingexam-controller"], "operationId": "update_4", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Upcomingexam"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Upcomingexam"}}}}}}, "delete": {"tags": ["upcomingexam-controller"], "operationId": "delete_4", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/testimonials/{id}": {"get": {"tags": ["testimonial-controller"], "operationId": "getById_5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Testimonial"}}}}}}, "put": {"tags": ["testimonial-controller"], "operationId": "update_5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Testimonial"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Testimonial"}}}}}}, "delete": {"tags": ["testimonial-controller"], "operationId": "delete_5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/studyMaterials/{id}": {"get": {"tags": ["studymaterial-controller"], "operationId": "getById_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Studymaterial"}}}}}}, "put": {"tags": ["studymaterial-controller"], "operationId": "update_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Studymaterial"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Studymaterial"}}}}}}, "delete": {"tags": ["studymaterial-controller"], "operationId": "delete_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/recentMaterials/{id}": {"get": {"tags": ["recentmaterial-controller"], "operationId": "getById_7", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Recentmaterial"}}}}}}, "put": {"tags": ["recentmaterial-controller"], "operationId": "update_7", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Recentmaterial"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Recentmaterial"}}}}}}, "delete": {"tags": ["recentmaterial-controller"], "operationId": "delete_7", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/recentActivities/{id}": {"get": {"tags": ["recentactivity-controller"], "operationId": "getById_8", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Recentactivity"}}}}}}, "put": {"tags": ["recentactivity-controller"], "operationId": "update_8", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Recentactivity"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Recentactivity"}}}}}}, "delete": {"tags": ["recentactivity-controller"], "operationId": "delete_8", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/questions/{id}": {"get": {"tags": ["question-controller"], "operationId": "getById_9", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Question"}}}}}}, "put": {"tags": ["question-controller"], "operationId": "update_9", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Question"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Question"}}}}}}, "delete": {"tags": ["question-controller"], "operationId": "delete_9", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/purchases/{id}": {"get": {"tags": ["purchase-controller"], "operationId": "getById_10", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Purchase"}}}}}}, "put": {"tags": ["purchase-controller"], "operationId": "update_10", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Purchase"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Purchase"}}}}}}, "delete": {"tags": ["purchase-controller"], "operationId": "delete_10", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/progressData/{id}": {"get": {"tags": ["progressdata-controller"], "operationId": "getById_11", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Progressdata"}}}}}}, "put": {"tags": ["progressdata-controller"], "operationId": "update_11", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Progressdata"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Progressdata"}}}}}}, "delete": {"tags": ["progressdata-controller"], "operationId": "delete_11", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/pricingPlans/{id}": {"get": {"tags": ["pricingplan-controller"], "operationId": "getById_12", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Pricingplan"}}}}}}, "put": {"tags": ["pricingplan-controller"], "operationId": "update_12", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pricingplan"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Pricingplan"}}}}}}, "delete": {"tags": ["pricingplan-controller"], "operationId": "delete_12", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/notifications/{id}": {"get": {"tags": ["notification-controller"], "operationId": "getById_13", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Notification"}}}}}}, "put": {"tags": ["notification-controller"], "operationId": "update_13", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Notification"}}}}}}, "delete": {"tags": ["notification-controller"], "operationId": "delete_13", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/newsItems/{id}": {"get": {"tags": ["newsitem-controller"], "operationId": "getById_14", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Newsitem"}}}}}}, "put": {"tags": ["newsitem-controller"], "operationId": "update_14", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Newsitem"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Newsitem"}}}}}}, "delete": {"tags": ["newsitem-controller"], "operationId": "delete_14", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/landingStats/{id}": {"get": {"tags": ["landingstat-controller"], "operationId": "getById_15", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Landingstat"}}}}}}, "put": {"tags": ["landingstat-controller"], "operationId": "update_15", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Landingstat"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Landingstat"}}}}}}, "delete": {"tags": ["landingstat-controller"], "operationId": "delete_15", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/landingFeatures/{id}": {"get": {"tags": ["landingfeature-controller"], "operationId": "getById_16", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Landingfeature"}}}}}}, "put": {"tags": ["landingfeature-controller"], "operationId": "update_16", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Landingfeature"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Landingfeature"}}}}}}, "delete": {"tags": ["landingfeature-controller"], "operationId": "delete_16", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/exams/{id}": {"get": {"tags": ["exam-controller"], "operationId": "getById_17", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Exam"}}}}}}, "put": {"tags": ["exam-controller"], "operationId": "update_17", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exam"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Exam"}}}}}}, "delete": {"tags": ["exam-controller"], "operationId": "delete_17", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/exam-results/{id}": {"get": {"tags": ["exam-result-controller"], "operationId": "getById_18", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExamResult"}}}}}}, "put": {"tags": ["exam-result-controller"], "operationId": "update_18", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExamResult"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExamResult"}}}}}}, "delete": {"tags": ["exam-result-controller"], "operationId": "delete_18", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/downloadLimits/{id}": {"get": {"tags": ["download-limit-controller"], "operationId": "getById_19", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DownloadLimit"}}}}}}, "put": {"tags": ["download-limit-controller"], "operationId": "update_19", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DownloadLimit"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DownloadLimit"}}}}}}, "delete": {"tags": ["download-limit-controller"], "operationId": "delete_19", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/downloadAttempts/{id}": {"get": {"tags": ["download-attempt-controller"], "operationId": "getById_20", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DownloadAttempt"}}}}}}, "put": {"tags": ["download-attempt-controller"], "operationId": "update_20", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DownloadAttempt"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DownloadAttempt"}}}}}}, "delete": {"tags": ["download-attempt-controller"], "operationId": "delete_20", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/demoQuestions/{id}": {"get": {"tags": ["demoquestion-controller"], "operationId": "getById_21", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Demoquestion"}}}}}}, "put": {"tags": ["demoquestion-controller"], "operationId": "update_21", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Demoquestion"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Demoquestion"}}}}}}, "delete": {"tags": ["demoquestion-controller"], "operationId": "delete_21", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/dailyQuizSubmissions/{id}": {"get": {"tags": ["dailyquizsubmission-controller"], "operationId": "getById_22", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Dailyquizsubmission"}}}}}}, "put": {"tags": ["dailyquizsubmission-controller"], "operationId": "update_22", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dailyquizsubmission"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Dailyquizsubmission"}}}}}}, "delete": {"tags": ["dailyquizsubmission-controller"], "operationId": "delete_22", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/dailyQuizQuestions/{id}": {"get": {"tags": ["dailyquizquestion-controller"], "operationId": "getById_23", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Dailyquizquestion"}}}}}}, "put": {"tags": ["dailyquizquestion-controller"], "operationId": "update_23", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dailyquizquestion"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Dailyquizquestion"}}}}}}, "delete": {"tags": ["dailyquizquestion-controller"], "operationId": "delete_23", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/adminStats/{id}": {"get": {"tags": ["adminstat-controller"], "operationId": "getById_24", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Adminstat"}}}}}}, "put": {"tags": ["adminstat-controller"], "operationId": "update_24", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Adminstat"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Adminstat"}}}}}}, "delete": {"tags": ["adminstat-controller"], "operationId": "delete_24", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/users": {"get": {"tags": ["user-controller"], "operationId": "getAll", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}}, "post": {"tags": ["user-controller"], "operationId": "create", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/api/userProfiles": {"get": {"tags": ["user-profile-controller"], "operationId": "getAll_1", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserProfile"}}}}}}}, "post": {"tags": ["user-profile-controller"], "operationId": "create_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}}}}}, "/api/userGrowthData": {"get": {"tags": ["usergrowthdata-controller"], "operationId": "getAll_2", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Usergrowthdata"}}}}}}}, "post": {"tags": ["usergrowthdata-controller"], "operationId": "create_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Usergrowthdata"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Usergrowthdata"}}}}}}}, "/api/userDailyQuizProgress": {"get": {"tags": ["userdailyquizprogre-controller"], "operationId": "getAll_3", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Userdailyquizprogre"}}}}}}}, "post": {"tags": ["userdailyquizprogre-controller"], "operationId": "create_3", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Userdailyquizprogre"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Userdailyquizprogre"}}}}}}}, "/api/upcomingExams": {"get": {"tags": ["upcomingexam-controller"], "operationId": "getAll_4", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Upcomingexam"}}}}}}}, "post": {"tags": ["upcomingexam-controller"], "operationId": "create_4", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Upcomingexam"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Upcomingexam"}}}}}}}, "/api/testimonials": {"get": {"tags": ["testimonial-controller"], "operationId": "getAll_5", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Testimonial"}}}}}}}, "post": {"tags": ["testimonial-controller"], "operationId": "create_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Testimonial"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Testimonial"}}}}}}}, "/api/studyMaterials": {"get": {"tags": ["studymaterial-controller"], "operationId": "getAll_6", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Studymaterial"}}}}}}}, "post": {"tags": ["studymaterial-controller"], "operationId": "create_6", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Studymaterial"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Studymaterial"}}}}}}}, "/api/studyMaterials/upload": {"post": {"tags": ["studymaterial-controller"], "operationId": "uploadMaterial", "parameters": [{"name": "title", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "description", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "subject", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "price", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "author", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "tags", "in": "query", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/studyMaterials/download-progress": {"post": {"tags": ["studymaterial-controller"], "operationId": "updateDownloadProgress", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "boolean"}}}}}}}}, "/api/studyMaterials/download-complete": {"post": {"tags": ["studymaterial-controller"], "operationId": "completeDownload", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/studyMaterials/download-attempts": {"post": {"tags": ["studymaterial-controller"], "operationId": "recordDownloadAttempt", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DownloadAttempt"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "boolean"}}}}}}}}, "/api/recentMaterials": {"get": {"tags": ["recentmaterial-controller"], "operationId": "getAll_7", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Recentmaterial"}}}}}}}, "post": {"tags": ["recentmaterial-controller"], "operationId": "create_7", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Recentmaterial"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Recentmaterial"}}}}}}}, "/api/recentActivities": {"get": {"tags": ["recentactivity-controller"], "operationId": "getAll_8", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Recentactivity"}}}}}}}, "post": {"tags": ["recentactivity-controller"], "operationId": "create_8", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Recentactivity"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Recentactivity"}}}}}}}, "/api/questions": {"get": {"tags": ["question-controller"], "operationId": "getAll_9", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Question"}}}}}}}, "post": {"tags": ["question-controller"], "operationId": "create_9", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Question"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Question"}}}}}}}, "/api/purchases": {"get": {"tags": ["purchase-controller"], "operationId": "getAll_10", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Purchase"}}}}}}}, "post": {"tags": ["purchase-controller"], "operationId": "create_10", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Purchase"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Purchase"}}}}}}}, "/api/progressData": {"get": {"tags": ["progressdata-controller"], "operationId": "getAll_11", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Progressdata"}}}}}}}, "post": {"tags": ["progressdata-controller"], "operationId": "create_11", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Progressdata"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Progressdata"}}}}}}}, "/api/pricingPlans": {"get": {"tags": ["pricingplan-controller"], "operationId": "getAll_12", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Pricingplan"}}}}}}}, "post": {"tags": ["pricingplan-controller"], "operationId": "create_12", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pricingplan"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Pricingplan"}}}}}}}, "/api/notifications": {"get": {"tags": ["notification-controller"], "operationId": "getAll_13", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}}}}}, "post": {"tags": ["notification-controller"], "operationId": "create_13", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Notification"}}}}}}}, "/api/newsItems": {"get": {"tags": ["newsitem-controller"], "operationId": "getAll_14", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Newsitem"}}}}}}}, "post": {"tags": ["newsitem-controller"], "operationId": "create_14", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Newsitem"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Newsitem"}}}}}}}, "/api/landingStats": {"get": {"tags": ["landingstat-controller"], "operationId": "getAll_15", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Landingstat"}}}}}}}, "post": {"tags": ["landingstat-controller"], "operationId": "create_15", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Landingstat"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Landingstat"}}}}}}}, "/api/landingFeatures": {"get": {"tags": ["landingfeature-controller"], "operationId": "getAll_16", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Landingfeature"}}}}}}}, "post": {"tags": ["landingfeature-controller"], "operationId": "create_16", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Landingfeature"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Landingfeature"}}}}}}}, "/api/exams": {"get": {"tags": ["exam-controller"], "operationId": "getAll_17", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Exam"}}}}}}}, "post": {"tags": ["exam-controller"], "operationId": "create_17", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exam"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Exam"}}}}}}}, "/api/exam-results": {"get": {"tags": ["exam-result-controller"], "operationId": "getAll_18", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExamResult"}}}}}}}, "post": {"tags": ["exam-result-controller"], "operationId": "create_18", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExamResult"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ExamResult"}}}}}}}, "/api/downloadLimits": {"get": {"tags": ["download-limit-controller"], "operationId": "getAll_19", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DownloadLimit"}}}}}}}, "post": {"tags": ["download-limit-controller"], "operationId": "create_19", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DownloadLimit"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DownloadLimit"}}}}}}}, "/api/downloadAttempts": {"get": {"tags": ["download-attempt-controller"], "operationId": "getAll_20", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DownloadAttempt"}}}}}}}, "post": {"tags": ["download-attempt-controller"], "operationId": "create_20", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DownloadAttempt"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DownloadAttempt"}}}}}}}, "/api/demoQuestions": {"get": {"tags": ["demoquestion-controller"], "operationId": "getAll_21", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Demoquestion"}}}}}}}, "post": {"tags": ["demoquestion-controller"], "operationId": "create_21", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Demoquestion"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Demoquestion"}}}}}}}, "/api/dailyQuizSubmissions": {"get": {"tags": ["dailyquizsubmission-controller"], "operationId": "getAll_22", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Dailyquizsubmission"}}}}}}}, "post": {"tags": ["dailyquizsubmission-controller"], "operationId": "create_22", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dailyquizsubmission"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Dailyquizsubmission"}}}}}}}, "/api/dailyQuizQuestions": {"get": {"tags": ["dailyquizquestion-controller"], "operationId": "getAll_23", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Dailyquizquestion"}}}}}}}, "post": {"tags": ["dailyquizquestion-controller"], "operationId": "create_23", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dailyquizquestion"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Dailyquizquestion"}}}}}}}, "/api/adminStats": {"get": {"tags": ["adminstat-controller"], "operationId": "getAll_24", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Adminstat"}}}}}}}, "post": {"tags": ["adminstat-controller"], "operationId": "create_24", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Adminstat"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Adminstat"}}}}}}}, "/api/studyMaterials/{id}/download": {"get": {"tags": ["studymaterial-controller"], "operationId": "downloadMaterial", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/studyMaterials/secure-download/{downloadId}": {"get": {"tags": ["studymaterial-controller"], "operationId": "secureDownload", "parameters": [{"name": "downloadId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "token", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/studyMaterials/download-limits/{userId}/{materialId}": {"get": {"tags": ["studymaterial-controller"], "operationId": "getDownloadLimits", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "materialId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/recentActivities/user/{user}": {"get": {"tags": ["recentactivity-controller"], "operationId": "getByUser", "parameters": [{"name": "user", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Recentactivity"}}}}}}}}, "/api/recentActivities/action/{action}": {"get": {"tags": ["recentactivity-controller"], "operationId": "getByAction", "parameters": [{"name": "action", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Recentactivity"}}}}}}}}, "/api/newsItems/importance/{importance}": {"get": {"tags": ["newsitem-controller"], "operationId": "getByImportance", "parameters": [{"name": "importance", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Newsitem"}}}}}}}}, "/api/newsItems/category/{category}": {"get": {"tags": ["newsitem-controller"], "operationId": "getByCategory", "parameters": [{"name": "category", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Newsitem"}}}}}}}}, "/api/exams/today": {"get": {"tags": ["exam-controller"], "operationId": "getTodayExams", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Exam"}}}}}}}}, "/api/exams/this-week": {"get": {"tags": ["exam-controller"], "operationId": "getThisWeekExams", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Exam"}}}}}}}}, "/api/exam-results/user/{userId}": {"get": {"tags": ["exam-result-controller"], "operationId": "getByUserId", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExamResult"}}}}}}}}, "/api/exam-results/exam/{examId}": {"get": {"tags": ["exam-result-controller"], "operationId": "getByExamId", "parameters": [{"name": "examId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ExamResult"}}}}}}}}, "/api/admin/recentMaterials": {"get": {"tags": ["admin-controller"], "operationId": "getRecentMaterials", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}}, "/api/admin/progressData": {"get": {"tags": ["admin-controller"], "operationId": "getProgressData", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "string"}, "firebaseUid": {"type": "string"}, "email": {"type": "string"}, "role": {"type": "string"}, "active": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "lastLoginAt": {"type": "string", "format": "date-time"}}}, "UserProfile": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "pincode": {"type": "string"}, "dateOfBirth": {"type": "string"}, "gender": {"type": "string"}, "qualification": {"type": "string"}, "targetExam": {"type": "string"}, "profilePicture": {"type": "string"}, "totalTests": {"type": "integer", "format": "int32"}, "completedTests": {"type": "integer", "format": "int32"}, "averageScore": {"type": "number", "format": "double"}, "rank": {"type": "integer", "format": "int32"}, "subscriptionType": {"type": "string"}, "avatar": {"type": "string"}, "subscriptionExpiry": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Usergrowthdata": {"type": "object", "properties": {"id": {"type": "string"}, "growthMonth": {"type": "string"}, "users": {"type": "integer", "format": "int32"}}}, "Userdailyquizprogre": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "totalQuizzesTaken": {"type": "integer", "format": "int32"}, "averageScore": {"type": "integer", "format": "int32"}, "currentStreak": {"type": "integer", "format": "int32"}, "bestStreak": {"type": "integer", "format": "int32"}, "weeklyProgress": {"type": "array", "items": {"type": "string"}}}}, "Upcomingexam": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "time": {"type": "string"}}}, "Testimonial": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "rank": {"type": "string"}, "image": {"type": "string"}, "quote": {"type": "string"}, "featured": {"type": "integer", "format": "int32"}}}, "Studymaterial": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "subject": {"type": "string"}, "price": {"type": "integer", "format": "int32"}, "originalPrice": {"type": "integer", "format": "int32"}, "rating": {"type": "number", "format": "double"}, "reviews": {"type": "integer", "format": "int32"}, "pages": {"type": "integer", "format": "int32"}, "author": {"type": "string"}, "isPremium": {"type": "integer", "format": "int32"}, "thumbnailUrl": {"type": "string"}, "status": {"type": "string"}, "downloads": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "fileSize": {"type": "string"}, "filePath": {"type": "string"}, "originalName": {"type": "string"}}}, "Recentmaterial": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}, "price": {"type": "string"}}}, "Recentactivity": {"type": "object", "properties": {"id": {"type": "string"}, "user": {"type": "string"}, "action": {"type": "string"}, "item": {"type": "string"}, "time": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}, "Question": {"type": "object", "properties": {"id": {"type": "string"}, "question": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}, "correctAnswer": {"type": "string"}, "explanation": {"type": "string"}, "subject": {"type": "string"}, "topic": {"type": "string"}, "category": {"type": "string"}, "difficulty": {"type": "string"}, "marks": {"type": "integer", "format": "int32"}, "negativeMarks": {"type": "integer", "format": "int32"}, "questionType": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "imageUrl": {"type": "string"}, "status": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Purchase": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "materialId": {"type": "string"}, "planId": {"type": "string"}, "orderId": {"type": "string"}, "paymentId": {"type": "string"}, "amount": {"type": "number", "format": "double"}, "currency": {"type": "string"}, "paymentMethod": {"type": "string"}, "status": {"type": "string"}, "purchaseDate": {"type": "string", "format": "date-time"}, "expiryDate": {"type": "string", "format": "date-time"}, "accessGranted": {"type": "boolean"}, "downloadCount": {"type": "integer", "format": "int32"}, "maxDownloads": {"type": "integer", "format": "int32"}, "transactionDetails": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Progressdata": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "progressMonth": {"type": "string"}, "score": {"type": "integer", "format": "int32"}, "totalQuestions": {"type": "integer", "format": "int32"}, "correctAnswers": {"type": "integer", "format": "int32"}, "wrongAnswers": {"type": "integer", "format": "int32"}, "skippedQuestions": {"type": "integer", "format": "int32"}, "accuracy": {"type": "number", "format": "double"}, "timeSpent": {"type": "integer", "format": "int32"}, "subject": {"type": "string"}, "category": {"type": "string"}, "rank": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Pricingplan": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "price": {"type": "string"}, "period": {"type": "string"}, "features": {"type": "array", "items": {"type": "string"}}, "popular": {"type": "integer", "format": "int32"}, "active": {"type": "integer", "format": "int32"}}}, "Notification": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "message": {"type": "string"}, "type": {"type": "string"}, "timestamp": {"type": "string"}, "read": {"type": "integer", "format": "int32"}, "audience": {"type": "string"}, "status": {"type": "string"}, "sentAt": {"type": "string", "format": "date-time"}, "recipients": {"type": "integer", "format": "int32"}, "openRate": {"type": "integer", "format": "int32"}}}, "Newsitem": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "category": {"type": "string"}, "date": {"type": "string"}, "summary": {"type": "string"}, "details": {"type": "string"}, "relevance": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "importance": {"type": "string"}}}, "Landingstat": {"type": "object", "properties": {"id": {"type": "string"}, "number": {"type": "string"}, "label": {"type": "string"}}}, "Landingfeature": {"type": "object", "properties": {"id": {"type": "string"}, "icon": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "color": {"type": "string"}}}, "Exam": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "subject": {"type": "string"}, "category": {"type": "string"}, "duration": {"type": "integer", "format": "int32"}, "totalQuestions": {"type": "integer", "format": "int32"}, "totalMarks": {"type": "integer", "format": "int32"}, "passingMarks": {"type": "integer", "format": "int32"}, "difficulty": {"type": "string"}, "status": {"type": "string"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "questionIds": {"type": "array", "items": {"type": "string"}}, "instructions": {"type": "string"}, "isPremium": {"type": "integer", "format": "int32"}, "attempts": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "ExamResult": {"type": "object", "properties": {"id": {"type": "string"}, "examId": {"type": "string"}, "userId": {"type": "string"}, "examTitle": {"type": "string"}, "score": {"type": "integer", "format": "int32"}, "totalQuestions": {"type": "integer", "format": "int32"}, "totalMarks": {"type": "integer", "format": "int32"}, "correctAnswers": {"type": "integer", "format": "int32"}, "incorrectAnswers": {"type": "integer", "format": "int32"}, "unanswered": {"type": "integer", "format": "int32"}, "timeTaken": {"type": "string"}, "rank": {"type": "integer", "format": "int32"}, "totalParticipants": {"type": "integer", "format": "int32"}, "percentile": {"type": "number", "format": "double"}, "subjects": {"type": "array", "items": {"$ref": "#/components/schemas/SubjectResult"}}, "completedAt": {"type": "string", "format": "date-time"}, "submittedAt": {"type": "string", "format": "date-time"}}}, "SubjectResult": {"type": "object", "properties": {"name": {"type": "string"}, "score": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "marks": {"type": "integer", "format": "int32"}, "totalMarks": {"type": "integer", "format": "int32"}}}, "DownloadLimit": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "materialId": {"type": "string"}, "maxDownloads": {"type": "integer", "format": "int32"}, "usedDownloads": {"type": "integer", "format": "int32"}, "lastDownloadAt": {"type": "string", "format": "date-time"}, "resetDate": {"type": "string", "format": "date-time"}}}, "DownloadAttempt": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "materialId": {"type": "string"}, "downloadId": {"type": "string"}, "attemptedAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time"}, "status": {"type": "string"}, "ipAddress": {"type": "string"}, "userAgent": {"type": "string"}, "progress": {"type": "integer", "format": "int32"}, "fileSize": {"type": "string"}, "downloadDuration": {"type": "integer", "format": "int32"}}}, "Demoquestion": {"type": "object", "properties": {"id": {"type": "string"}, "question": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}, "correctAnswer": {"type": "integer", "format": "int32"}, "explanation": {"type": "string"}}}, "Dailyquizsubmission": {"type": "object", "properties": {"id": {"type": "string"}, "userId": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "score": {"type": "integer", "format": "int32"}, "totalQuestions": {"type": "integer", "format": "int32"}, "answers": {"type": "array", "items": {"type": "string"}}, "timeSpent": {"type": "integer", "format": "int32"}, "submittedAt": {"type": "string", "format": "date-time"}}}, "Dailyquizquestion": {"type": "object", "properties": {"id": {"type": "string"}, "date": {"type": "string", "format": "date-time"}, "question": {"type": "string"}, "options": {"type": "array", "items": {"type": "string"}}, "correctAnswer": {"type": "integer", "format": "int32"}, "explanation": {"type": "string"}, "subject": {"type": "string"}}}, "Adminstat": {"type": "object", "properties": {"id": {"type": "string"}, "label": {"type": "string"}, "value": {"type": "string"}, "icon": {"type": "string"}, "color": {"type": "string"}, "trend": {"type": "string"}, "percentage": {"type": "number", "format": "double"}, "description": {"type": "string"}, "lastUpdated": {"type": "string", "format": "date-time"}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}