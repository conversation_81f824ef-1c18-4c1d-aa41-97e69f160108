package com.sparkminds.brainstorm.upsc.config;

import com.sparkminds.brainstorm.upsc.security.FirebaseAuthenticationFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.server.WebFilter;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

import static org.springframework.security.config.web.server.SecurityWebFiltersOrder.AUTHENTICATION;

@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {

    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http,
                                                         FirebaseAuthenticationFilter firebaseFilter) {
        http
                .csrf(ServerHttpSecurity.CsrfSpec::disable)
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                .authorizeExchange(exchange -> exchange
                        .pathMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                        .pathMatchers("/swagger-ui/**", "/v3/api-docs", "/v3/api-docs/**").permitAll()
                        .anyExchange().permitAll()
                )
                .addFilterAt(firebaseFilter, AUTHENTICATION)
                .addFilterBefore(requestLoggingFilter(), AUTHENTICATION);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowedOrigins(List.of("http://localhost:5173","https://brainstorm-upsc-466216.el.r.appspot.com","https://brainstormupsc.com","https://www.brainstormupsc.com")); // ✅ not "*"
        config.setAllowedMethods(List.of("GET", "POST", "PUT", "DELETE", "OPTIONS" , "PATCH"));
        config.setAllowedHeaders(List.of("*"));
        config.setAllowCredentials(true); // ✅ Important if using cookies or auth headers

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        return source;
    }


    @Bean
    public WebFilter requestLoggingFilter() {
        return (exchange, chain) -> {
            System.out.println("[SECURITY CONFIG] Request: " +
                    exchange.getRequest().getMethod() + " " + exchange.getRequest().getURI());
            String authHeader = exchange.getRequest().getHeaders().getFirst("Authorization");
            System.out.println("[SECURITY CONFIG] Authorization header: " + authHeader);

            return chain.filter(exchange)
                    .doOnTerminate(() -> {
                        var status = exchange.getResponse().getStatusCode();
                        System.out.println("[SECURITY CONFIG] Response status: " + (status != null ? status.value() : "null"));
                    });
        };
    }
}
