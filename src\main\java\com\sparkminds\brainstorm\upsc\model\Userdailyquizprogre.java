package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.*;

@Document(collectionName = "user_daily_quiz_progress")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Userdailyquizprogre {
    @DocumentId
    private String id;
    private String userId;
    private Integer totalQuizzesTaken;
    private Double  averageScore;
    private Integer currentStreak;
    private Integer bestStreak;
    @Getter
    private List<Object> weeklyProgress;

}