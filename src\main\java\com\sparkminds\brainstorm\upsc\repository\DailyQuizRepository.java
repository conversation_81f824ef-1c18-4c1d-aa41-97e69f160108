package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.DailyQuiz;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import java.util.Date;

public interface DailyQuizRepository extends FirestoreReactiveRepository<DailyQuiz> {
    Flux<DailyQuiz> findByDateGreaterThanEqualAndDateLessThan(Date startDate, Date endDate);
}