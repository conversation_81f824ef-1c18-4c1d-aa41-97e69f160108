package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.DownloadAttempt;
import com.sparkminds.brainstorm.upsc.repository.DownloadAttemptRepository;

@Service
public class DownloadAttemptService {
    private final DownloadAttemptRepository repository;
    
    public DownloadAttemptService(DownloadAttemptRepository repository) { 
        this.repository = repository; 
    }
    
    public Flux<DownloadAttempt> findAll() { 
        return repository.findAll(); 
    }
    
    public Mono<DownloadAttempt> findById(String id) { 
        return repository.findById(id); 
    }
    
    public Mono<DownloadAttempt> save(DownloadAttempt obj) { 
        return repository.save(obj); 
    }
    
    public Mono<Void> delete(String id) { 
        return repository.deleteById(id); 
    }
}