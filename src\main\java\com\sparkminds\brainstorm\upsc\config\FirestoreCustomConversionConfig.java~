package com.sparkminds.brainstorm.upsc.config;

import com.google.cloud.Timestamp;
import com.google.cloud.spring.data.firestore.FirestoreTemplate;
import com.google.cloud.spring.data.firestore.mapping.FirestoreMappingContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.CustomConversions;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

@Configuration
public class FirestoreCustomConversionConfig {

    @Bean
    public CustomConversions firestoreCustomConversions() {
        List<Converter<?, ?>> converters = new ArrayList<>();

        // Firestore Timestamp to LocalDateTime
        converters.add(new Converter<Timestamp, LocalDateTime>() {
            @Override
            public LocalDateTime convert(Timestamp source) {
                return source.toSqlTimestamp().toLocalDateTime();
            }
        });

        // LocalDateTime to Firestore Timestamp
        converters.add(new Converter<LocalDateTime, Timestamp>() {
            @Override
            public Timestamp convert(LocalDateTime source) {
                Instant instant = source.toInstant(ZoneOffset.UTC);
                return Timestamp.ofTimeSecondsAndNanos(instant.getEpochSecond(), instant.getNano());
            }
        });

        return new CustomConversions(CustomConversions.StoreConversions.NONE, converters);
    }

    // FirestoreTemplate bean is auto-configured; just make sure it's not excluded
}
