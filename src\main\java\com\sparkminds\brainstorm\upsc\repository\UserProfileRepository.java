package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.UserProfile;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface UserProfileRepository extends FirestoreReactiveRepository<UserProfile> {
    Mono<UserProfile> findByUserId(String userId);
    Flux<UserProfile> findByTargetExam(String targetExam);
    Flux<UserProfile> findBySubscriptionType(String subscriptionType);
    Flux<UserProfile> findByEmail(String email);
}