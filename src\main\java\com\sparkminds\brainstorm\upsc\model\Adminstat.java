package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;

@Document(collectionName = "admin_stats")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Adminstat {
    @DocumentId
    private String id;
    private String label;
    private String value;
    private String icon;
    private String color;
    private String trend; // up, down, stable
    private Double percentage;
    private String description;
    private LocalDateTime lastUpdated;
}
