package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

@Document(collectionName = "pricing_plans")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Pricingplan {
    @DocumentId
    private String id;
    private String name;
    private String price;
    private String period;
    private List<String> features;
    private Integer popular;
    private Integer active;
}
