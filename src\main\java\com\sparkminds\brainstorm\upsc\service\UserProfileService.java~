package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.model.User;
import org.springframework.stereotype.Service;

import com.sparkminds.brainstorm.upsc.model.UserProfile;
import com.sparkminds.brainstorm.upsc.repository.UserProfileRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class UserProfileService {
    public final UserProfileRepository repository;
    public UserProfileService(UserProfileRepository repository) { this.repository = repository; }
    public Flux<UserProfile> findAll() { return repository.findAll(); }
    public Mono<UserProfile> findById(String id) { return repository.findById(id); }
    public Mono<UserProfile> save(UserProfile obj) { return repository.save(obj); }
    public Mono<Void> delete(String id) { repository.deleteById(id);
        return null;
    }

    public Mono<UserProfile> findByUserId(Object userId) {
        String id = null;
        if (userId instanceof User) {
            id = ((User) userId).getId();
        } else if (userId instanceof String) {
            id = (String) userId;
        } else {
            return Mono.error(new RuntimeException("Invalid userId type: " + userId.getClass().getName()));
        }

        return repository.findByUserId(id)
            .switchIfEmpty(Mono.error(new RuntimeException("User profile not found for userId: " + id)));
    }
}