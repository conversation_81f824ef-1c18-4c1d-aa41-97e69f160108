package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.DownloadLimit;
import reactor.core.publisher.Mono;

public interface DownloadLimitRepository extends FirestoreReactiveRepository<DownloadLimit> {
    Mono<DownloadLimit> findByUserIdAndMaterialId(String userId, String materialId);
}
