package com.sparkminds.brainstorm.upsc.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service
public class PDFService {
    private static final Logger log = LoggerFactory.getLogger(PDFService.class);
    private final WebClient webClient;
    private static final String BASE_URL = "https://sparkmate-image-service-158250499449.asia-south1.run.app";

    public PDFService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.baseUrl(BASE_URL).build();
    }

    public Mono<PDFDocument> uploadPDF(FilePart file, String userId, String category) {
        log.info("Uploading PDF to SparkMate image service, userId: {}, category: {}", userId, category);

        try {
            MultipartBodyBuilder builder = new MultipartBodyBuilder();
            builder.part("file", file, MediaType.APPLICATION_PDF)
                    .header("Content-Disposition", "form-data; name=file; filename=" + file.filename());
            builder.part("userId", userId);
            builder.part("category", category);

            return webClient.post()
                    .uri("/api/v1/pdf/upload")
                    .contentType(MediaType.MULTIPART_FORM_DATA)
                    .bodyValue(builder.build())
                    .retrieve()
                    .bodyToMono(PDFDocumentResponse.class)
                    .map(response -> {
                        PDFDocument doc = response.getTransaction();
                        log.info("PDF upload successful, public_url: {}", doc.getPublic_url());
                        if (!doc.getPublic_url().contains("fl_attachment")) {
                            String modifiedUrl = doc.getSecure_url().replace("/upload/", "/upload/fl_attachment/");
                            doc.setPublic_url(modifiedUrl);
                            log.info("Modified public_url with fl_attachment: {}", modifiedUrl);
                        }
                        return doc;
                    })
                    .doOnError(error -> log.error("Failed to upload PDF: {}", error.getMessage(), error));
        } catch (Exception e) {
            log.error("Error preparing PDF upload: {}", e.getMessage(), e);
            return Mono.error(new RuntimeException("Failed to prepare PDF upload: " + e.getMessage()));
        }
    }

    public static class PDFDocumentResponse {
        private PDFDocument transaction;

        public PDFDocument getTransaction() {
            return transaction;
        }

        public void setTransaction(PDFDocument transaction) {
            this.transaction = transaction;
        }
    }

    public static class PDFDocument {
        private String id;
        private int user_id;
        private String original_file_name;
        private String cloudinary_public_id;
        private String secure_url;
        private String public_url;
        private long file_size;
        private String status;
        private String content_type;
        private long upload_duration_ms;
        private String created_at;
        private String updated_at;

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public int getUser_id() { return user_id; }
        public void setUser_id(int user_id) { this.user_id = user_id; }

        public String getOriginal_file_name() { return original_file_name; }
        public void setOriginal_file_name(String original_file_name) { this.original_file_name = original_file_name; }

        public String getCloudinary_public_id() { return cloudinary_public_id; }
        public void setCloudinary_public_id(String cloudinary_public_id) { this.cloudinary_public_id = cloudinary_public_id; }

        public String getSecure_url() { return secure_url; }
        public void setSecure_url(String secure_url) { this.secure_url = secure_url; }

        public String getPublic_url() { return public_url; }
        public void setPublic_url(String public_url) { this.public_url = public_url; }

        public long getFile_size() { return file_size; }
        public void setFile_size(long file_size) { this.file_size = file_size; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public String getContent_type() { return content_type; }
        public void setContent_type(String content_type) { this.content_type = content_type; }

        public long getUpload_duration_ms() { return upload_duration_ms; }
        public void setUpload_duration_ms(long upload_duration_ms) { this.upload_duration_ms = upload_duration_ms; }

        public String getCreated_at() { return created_at; }
        public void setCreated_at(String created_at) { this.created_at = created_at; }

        public String getUpdated_at() { return updated_at; }
        public void setUpdated_at(String updated_at) { this.updated_at = updated_at; }
    }
}
