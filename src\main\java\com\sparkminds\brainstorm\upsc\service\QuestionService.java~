package com.sparkminds.brainstorm.upsc.service;

import com.google.cloud.firestore.Firestore;
import com.sparkminds.brainstorm.upsc.model.Exam;
import com.sparkminds.brainstorm.upsc.model.Question;
import com.sparkminds.brainstorm.upsc.repository.ExamRepository;
import com.sparkminds.brainstorm.upsc.repository.QuestionRepository;
import com.sparkminds.brainstorm.upsc.util.CsvUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.buffer.DataBuffer; // ADDED
import org.springframework.core.io.buffer.DataBufferUtils; // ADDED
import org.springframework.http.codec.multipart.FilePart; // ADDED
import org.springframework.stereotype.Service;
// REMOVED: import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.List;

@Service
public class QuestionService {
    private static final Logger log = LoggerFactory.getLogger(QuestionService.class);
    private final QuestionRepository repository;
    private final ExamRepository examRepository;
    private final Firestore firestore; // ADDED: Firestore client for direct operations

    public QuestionService(QuestionRepository repository, ExamRepository examRepository , Firestore firestore) {
        this.repository = repository;
        this.examRepository = examRepository;
        this.firestore = firestore;
    }

    // ... (no changes to other methods)
    public Flux<Question> findAll() {
        return repository.findAll()
                .doOnNext(question -> log.debug("Retrieved question: {}", question.getId()));
    }

    public Mono<Question> findById(String id) {
        return repository.findById(id)
                .switchIfEmpty(Mono.error(new RuntimeException("Question not found: " + id)))
                .doOnNext(question -> log.debug("Retrieved question by ID: {}", id));
    }

    public Mono<Question> save(Question question) {
        if (question.getId() == null) {
            question.setCreatedAt(new Date());
            question.setStatus("active");
        }
        question.setUpdatedAt(new Date());
        return repository.save(question)
                .doOnSuccess(saved -> log.info("Question saved with ID: {}", saved.getId()))
                .doOnError(error -> log.error("Failed to save question: {}", error.getMessage()));
    }

    public Mono<Void> delete(String id) {
        return repository.findById(id)
                .flatMap(question -> repository.deleteById(id)
                        .doOnSuccess(v -> log.info("Question deleted with ID: {}", id)))
                .switchIfEmpty(Mono.error(new RuntimeException("Question not found: " + id)))
                .doOnError(error -> log.error("Failed to delete question: {}", error.getMessage()));
    }

    public Flux<Question> findByExamId(String examId) {
        return repository.findByExamId(examId)
                .doOnNext(question -> log.debug("Found question for exam {}: {}", examId, question.getId()))
                .switchIfEmpty(Flux.error(new RuntimeException("No questions found for exam: " + examId)));
    }


    // CHANGED: Method signature accepts FilePart
    public Mono<List<Question>> uploadQuestionsFromCsv(String examId, FilePart filePart) {
        return examRepository.findById(examId)
                .switchIfEmpty(Mono.error(new RuntimeException("Exam not found: " + examId)))
                .flatMap(exam -> {
                    // CHANGED: Convert reactive FilePart to a standard InputStream
                    return DataBufferUtils.join(filePart.content())
                            .map(dataBuffer -> {
                                try {
                                    // The 'true' argument ensures the underlying buffer is released after the stream is consumed
                                    return CsvUtil.parseQuestionsFromCsv(dataBuffer.asInputStream(true), examId);
                                } catch (Exception e) {
                                    log.error("CSV upload error: {}", e.getMessage());
                                    throw new IllegalArgumentException(e.getMessage());
                                }
                            })
                            .flatMap(questions -> Flux.fromIterable(questions)
                                    .flatMap(this::save)
                                    .collectList()
                                    .flatMap(savedQuestions -> {
                                        exam.getQuestionIds().addAll(savedQuestions.stream().map(Question::getId).toList());
                                        exam.setTotalQuestions(exam.getQuestionIds().size());
                                        exam.setUpdatedAt(new Date());
                                        return examRepository.save(exam)
                                                .thenReturn(savedQuestions)
                                                .doOnSuccess(saved -> log.info("Saved {} questions for exam {}", savedQuestions.size(), examId))
                                                .doOnError(error -> log.error("Failed to save questions for exam {}: {}", examId, error.getMessage()));
                                    }));
                });
    }
}