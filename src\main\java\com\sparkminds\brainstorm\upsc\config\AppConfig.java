package com.sparkminds.brainstorm.upsc.config;

import com.sparkminds.brainstorm.upsc.service.PDFService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class AppConfig {
    @Bean
    public PDFService pdfService(WebClient.Builder webClientBuilder) {
        return new PDFService(webClientBuilder);
    }
}