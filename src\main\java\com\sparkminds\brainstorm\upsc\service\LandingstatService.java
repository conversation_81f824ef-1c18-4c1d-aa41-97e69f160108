package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.sparkminds.brainstorm.upsc.model.Landingstat;
import com.sparkminds.brainstorm.upsc.repository.LandingstatRepository;

@Service
public class LandingstatService {
    private final LandingstatRepository repository;

    public LandingstatService(LandingstatRepository repository) {
        this.repository = repository;
    }

    public Flux<Landingstat> findAll() {
        return repository.findAll();
    }

    public Mono<Landingstat> findById(String id) {
        return repository.findById(id);
    }

    public Mono<Landingstat> save(Landingstat obj) {
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }
}