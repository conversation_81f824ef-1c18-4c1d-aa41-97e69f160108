package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.model.Exam;
import com.sparkminds.brainstorm.upsc.repository.ExamRepository;
import com.sparkminds.brainstorm.upsc.repository.PurchaseRepository;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Date;

@Service
public class ExamService {
    private static final Logger log = LoggerFactory.getLogger(ExamService.class);
    private final ExamRepository repository;
    private final PurchaseRepository purchaseRepository;

    public ExamService(ExamRepository repository, PurchaseRepository purchaseRepository) {
        this.repository = repository;
        this.purchaseRepository = purchaseRepository;
    }

    public Flux<Exam> findAll() {
        return repository.findAll()
                .doOnNext(exam -> log.debug("Retrieved exam: {}", exam.getId()));
    }

    public Flux<Exam> findUpcomingExams() {
        LocalDateTime now = LocalDateTime.now();
        return repository.findTop4ByStatusAndStartTimeGreaterThanOrderByStartTimeAsc("active", now)
                .doOnNext(exam -> log.debug("Found upcoming exam: {}", exam.getTitle()))
                .switchIfEmpty(Flux.empty());
    }

    public Mono<Exam> findById(String id) {
        return repository.findById(id)
                .switchIfEmpty(Mono.error(new RuntimeException("Exam not found: " + id)))
                .doOnNext(exam -> log.debug("Retrieved exam by ID: {}", id));
    }

    public Mono<Exam> save(Exam exam) {
        if (exam.getId() == null) {
            exam.setCreatedAt(new Date());
            exam.setAttempts(0);
            exam.setStatus("active");
            // Set default prices for non-premium exams
            if (exam.getIsPremium() == null || exam.getIsPremium() == 0) {
                exam.setBasePrice(0.0);
                exam.setOfferPrice(0.0);
            } else {
                // Validate pricing for premium exams
                if (exam.getBasePrice() == null || exam.getBasePrice() <= 0) {
                    throw new IllegalArgumentException("Premium exams must have a positive basePrice");
                }
                if (exam.getOfferPrice() == null) {
                    exam.setOfferPrice(exam.getBasePrice()); // Default to basePrice if offerPrice not set
                } else if (exam.getOfferPrice() > exam.getBasePrice()) {
                    throw new IllegalArgumentException("offerPrice cannot exceed basePrice");
                }
            }
        }
        exam.setUpdatedAt(new Date());
        return repository.save(exam)
                .doOnSuccess(saved -> log.info("Exam saved with ID: {}", saved.getId()))
                .doOnError(error -> log.error("Failed to save exam: {}", error.getMessage()));
    }

    public Mono<Void> delete(String id) {
        return repository.findById(id)
                .flatMap(exam -> repository.deleteById(id)
                        .doOnSuccess(v -> log.info("Exam deleted with ID: {}", id)))
                .switchIfEmpty(Mono.error(new RuntimeException("Exam not found: " + id)))
                .doOnError(error -> log.error("Failed to delete exam: {}", error.getMessage()));
    }

    public Flux<Exam> findTodayExams() {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        return repository.findByStartTimeGreaterThanEqualAndStartTimeLessThanEqual(startOfDay, endOfDay)
                .doOnNext(exam -> log.debug("Found exam for today: {}", exam.getId()));
    }

    public Flux<Exam> findThisWeekExams() {
        LocalDateTime startOfWeek = LocalDateTime.now().minusDays(7).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfWeek = LocalDateTime.now().plusDays(7).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        return repository.findByStartTimeGreaterThanEqualAndStartTimeLessThanEqual(startOfWeek, endOfWeek)
                .doOnNext(exam -> log.debug("Found exam for this week: {}", exam.getId()));
    }

    public Flux<Exam> findByStatus(String status) {
        return repository.findByStatus(status)
                .doOnNext(exam -> log.debug("Found exam with status {}: {}", status, exam.getId()));
    }

    public Flux<Exam> findByCategory(String category) {
        return repository.findByCategory(category)
                .doOnNext(exam -> log.debug("Found exam with category {}: {}", category, exam.getId()));
    }

    public Flux<Exam> findByIsPremium(Integer isPremium) {
        return repository.findByIsPremium(isPremium)
                .doOnNext(exam -> log.debug("Found exam with isPremium {}: {}", isPremium, exam.getId()));
    }

    public Mono<Exam> incrementAttemptCount(String id) {
        return findById(id)
                .flatMap(exam -> {
                    exam.setAttempts(exam.getAttempts() + 1);
                    return save(exam);
                })
                .doOnSuccess(exam -> log.info("Incremented attempt count for exam: {}", id))
                .doOnError(error -> log.error("Failed to increment attempt count for exam {}: {}", id, error.getMessage()));
    }

    public Mono<Boolean> canUserAccessExam(String userId, String examId) {
        return repository.findById(examId)
                .flatMap(exam -> {
                    if (exam.getIsPremium() == 0) {
                        return Mono.just(true); // Non-premium exams are accessible to all
                    }
                    return purchaseRepository.findByUserIdAndMaterialId(userId, examId)
                            .map(purchase -> purchase.getStatus().equals("completed") && purchase.getAccessGranted())
                            .defaultIfEmpty(false)
                            .doOnNext(canAccess -> log.info("Can user {} access exam {}? {}", userId, examId, canAccess));
                })
                .switchIfEmpty(Mono.just(false))
                .doOnError(error -> log.error("Error checking access for exam {} by user {}: {}", examId, userId, error.getMessage()));
    }

    public Mono<Exam> startExam(String examId) {
        return SecurityUtils.getCurrentUserFirebaseUid()
                .switchIfEmpty(Mono.error(new IllegalStateException("User not authenticated")))
                .flatMap(userId -> canUserAccessExam(userId, examId)
                        .flatMap(canAccess -> {
                            if (!Boolean.TRUE.equals(canAccess)) {
                                return Mono.error(new IllegalStateException("User does not have access to this exam"));
                            }
                            return findById(examId)
                                    .flatMap(exam -> {
                                        if (!"active".equals(exam.getStatus())) {
                                            return Mono.error(new IllegalStateException("This exam is not currently active."));
                                        }

                                        LocalDateTime now = LocalDateTime.now();
                                        LocalDateTime startTime = exam.getStartTimeAsLocalDateTime();

                                        if (startTime != null && now.isBefore(startTime)) {
                                            return Mono.error(new IllegalStateException("This exam has not started yet."));
                                        }
                                        return incrementAttemptCount(examId);
                                    });
                        }))
                .doOnError(error -> log.error("Failed to start exam {}: {}", examId, error.getMessage()));
    }

    
}