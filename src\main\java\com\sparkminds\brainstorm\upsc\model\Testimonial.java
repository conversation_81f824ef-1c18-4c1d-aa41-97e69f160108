package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Document(collectionName = "testimonials")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Testimonial {
    @DocumentId
    private String id;
    private String name;
    private String rank;
    private String image;
    private String quote;
    private Object featured;

    public Integer getFeaturedAsInteger() {
        if (featured == null) return null;
        if (featured instanceof Integer) return (Integer) featured;
        if (featured instanceof Boolean) return (Boolean) featured ? 1 : 0;
        if (featured instanceof String) {
            try {
                return Integer.parseInt((String) featured);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }
}
