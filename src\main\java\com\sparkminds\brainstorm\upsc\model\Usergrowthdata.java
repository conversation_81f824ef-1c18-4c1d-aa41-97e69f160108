package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Document(collectionName = "user_growth_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Usergrowthdata {
    @DocumentId
    private String id;
    private String growthMonth;
    private Integer users;
}