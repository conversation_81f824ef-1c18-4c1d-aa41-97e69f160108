package com.sparkminds.brainstorm.upsc.dto;

import com.sparkminds.brainstorm.upsc.model.DownloadAttempt;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseWithDownloadAttempts {
    // Purchase fields from payment gateway service
    private String id;
    private String userId;
    private String materialId;
    private Double amount;
    private String paymentId;
    private String orderId;
    private String status;
    private LocalDateTime purchaseDate;
    private String paymentMethod;
    private String gateway;
    private Integer downloadCount;
    private LocalDateTime lastDownloadAt;
    private String appName;
    private String appId;
    private String invoiceId;
    private String bulkOrderId;
    
    // Razorpay mapping fields
    private Boolean razorpayDataFound;
    private String mappingError;
    private RazorpayPaymentData razorpayPaymentData;
    
    // NEW: Download attempts array
    private List<DownloadAttempt> downloadAttempts;
}
