package com.sparkminds.brainstorm.upsc.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Builder
public class DashboardResponse {
    private String userId;
    private int totalQuizzesTaken;
    private double averageQuizScore;
    private int currentStreak;
    private int bestStreak;
    private int totalExamsTaken;
    private double averageExamScore;
    private int totalCorrectAnswers;
    private int totalIncorrectAnswers;
    private int totalUnanswered;
    private LocalDateTime lastUpdated;
}