package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;

import com.sparkminds.brainstorm.upsc.model.Dailyquizquestion;
import com.sparkminds.brainstorm.upsc.repository.DailyquizquestionRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class DailyquizquestionService {
    private final DailyquizquestionRepository repository;
    public DailyquizquestionService(DailyquizquestionRepository repository) { this.repository = repository; }
    public Flux<Dailyquizquestion> findAll() { return repository.findAll(); }
    public Mono<Dailyquizquestion> findById(String id) { return repository.findById(id); }
    public Mono<Dailyquizquestion> save(Dailyquizquestion obj) { return repository.save(obj); }
    public Mono<Void> delete(String id) { repository.deleteById(id);
        return null;
    }
}