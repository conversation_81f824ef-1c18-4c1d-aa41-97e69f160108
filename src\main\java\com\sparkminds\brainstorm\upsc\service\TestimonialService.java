package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Testimonial;
import com.sparkminds.brainstorm.upsc.repository.TestimonialRepository;

@Service
public class TestimonialService {
    private final TestimonialRepository repository;
    
    public TestimonialService(TestimonialRepository repository) { 
        this.repository = repository; 
    }
    
    public Flux<Testimonial> findAll() { 
        return repository.findAll(); 
    }
    
    public Mono<Testimonial> findById(String id) { 
        return repository.findById(id); 
    }
    
    public Mono<Testimonial> save(Testimonial obj) { 
        return repository.save(obj); 
    }
    
    public Mono<Void> delete(String id) { 
        return repository.deleteById(id); 
    }
}