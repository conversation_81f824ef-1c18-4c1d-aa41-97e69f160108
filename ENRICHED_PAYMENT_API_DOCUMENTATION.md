# Enriched Payment API Documentation

## New Endpoint: Fetch Purchases with Download Attempts

### Overview
This endpoint fetches purchase data from the external payment gateway service and enriches it with complete download attempt records from the local database. It provides a comprehensive view of user purchases along with their download history.

### Endpoint Details
- **URL**: `GET /api/payments/purchases/enriched`
- **Method**: GET
- **Content-Type**: application/json
- **Server**: `http://localhost:8081`

### Default Behavior
- **If no parameters provided**: Returns today's purchase data only
- **If parameters provided**: Returns filtered data based on the parameters

### Query Parameters

#### New Pagination Parameters (Recommended - matches Razorpay API pattern)
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| from | Long | No | today start | Unix timestamp - filter purchases created after this time |
| to | Long | No | today end | Unix timestamp - filter purchases created before this time |
| count | Integer | No | 10 | Number of purchases to return (max: 100) |
| skip | Integer | No | 0 | Number of purchases to skip for pagination |

#### Legacy Pagination Parameters (Backward Compatibility)
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| limit | Integer | No | 50 | Number of purchases to return (max: 200) |
| offset | Integer | No | 0 | Number of purchases to skip for pagination |

#### Filter Parameters
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| userId | String | No | null | Filter purchases by user ID |
| status | String | No | null | Filter purchases by status (pending, completed, failed, etc.) |
| includeRazorpayData | Boolean | No | true | Whether to fetch and include Razorpay payment details |

### Data Flow
1. **Fetch from External API**: Calls payment gateway service with provided parameters
2. **Enrich with Download Attempts**: For each purchase, fetches all related download attempts from local database
3. **Return Combined Data**: Returns purchase data with complete download attempt records

### Response Format

```json
{
  "entity": "collection",
  "totalCount": 100,
  "returnedCount": 10,
  "offset": 0,
  "limit": 10,
  "hasMore": true,
  "razorpayDataFoundCount": 8,
  "razorpayDataMissingCount": 2,
  "items": [
    {
      "id": "purchase_123",
      "userId": "user_456",
      "materialId": "material_789",
      "amount": 1000.0,
      "paymentId": "pay_RPG9JFCFfQSKWX",
      "orderId": "order_RPG9EANR8KM93Z",
      "status": "completed",
      "purchaseDate": "2025-10-04T10:30:00Z",
      "paymentMethod": "upi",
      "gateway": "razorpay",
      "downloadCount": 3,
      "lastDownloadAt": "2025-10-04T11:00:00Z",
      "appName": "MyApp",
      "appId": "app_123",
      "invoiceId": "inv_456",
      "bulkOrderId": null,
      "razorpayDataFound": true,
      "mappingError": null,
      "razorpayPaymentData": {
        "id": "pay_RPG9JFCFfQSKWX",
        "entity": "payment",
        "amount": 100000,
        "currency": "INR",
        "status": "captured",
        "orderId": "order_RPG9EANR8KM93Z",
        "method": "upi",
        "email": "<EMAIL>",
        "contact": "+919876543210",
        "createdAt": 1696410600
      },
      "downloadAttempts": [
        {
          "id": "attempt_1",
          "userId": "user_456",
          "materialId": "material_789",
          "downloadId": "download_123",
          "attemptedAt": "2025-10-04T10:30:00Z",
          "completedAt": "2025-10-04T10:35:00Z",
          "status": "completed",
          "ipAddress": "***********",
          "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
          "progress": 100,
          "fileSize": "5MB",
          "downloadDuration": 300
        },
        {
          "id": "attempt_2",
          "userId": "user_456",
          "materialId": "material_789",
          "downloadId": "download_124",
          "attemptedAt": "2025-10-04T11:00:00Z",
          "completedAt": null,
          "status": "failed",
          "ipAddress": "***********",
          "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
          "progress": 45,
          "fileSize": "5MB",
          "downloadDuration": null
        }
      ]
    }
  ]
}
```

### Example Requests

#### 1. Default Request (Today's Data)
```bash
curl -X GET "http://localhost:8081/api/payments/purchases/enriched"
```

#### 2. Basic Request with New Pagination
```bash
curl -X GET "http://localhost:8081/api/payments/purchases/enriched?count=10&skip=0"
```

#### 3. Date Range Filtering
```bash
curl -X GET "http://localhost:8081/api/payments/purchases/enriched?from=1693440000&to=1696118400&count=20"
```

#### 4. Filter by User ID
```bash
curl -X GET "http://localhost:8081/api/payments/purchases/enriched?userId=user_123&count=15&skip=5"
```

#### 5. Legacy Pagination Style
```bash
curl -X GET "http://localhost:8081/api/payments/purchases/enriched?limit=10&offset=0"
```

#### 6. Without Razorpay Data (Faster Response)
```bash
curl -X GET "http://localhost:8081/api/payments/purchases/enriched?count=50&includeRazorpayData=false"
```

### Key Features

1. **Default Today's Data**: Automatically shows today's purchases if no parameters provided
2. **External API Integration**: Fetches purchase data from payment gateway service
3. **Download Attempts Enrichment**: Adds complete download attempt records for each purchase
4. **Dual Pagination Support**: Both new (Razorpay-style) and legacy pagination
5. **Comprehensive Filtering**: Date range, user ID, and status filtering
6. **Error Resilience**: Continues processing even if external API or local database queries fail

### Performance Notes

- **External API Dependency**: Response time depends on the external payment gateway service
- **Download Attempts Query**: Each purchase triggers a query to fetch related download attempts
- **Error Handling**: If external API fails, returns empty response; if download attempts fail, returns empty array
- **Logging**: Comprehensive logging for debugging and monitoring

### Status Codes
- **200 OK**: Request successful
- **400 Bad Request**: Invalid parameters
- **500 Internal Server Error**: Server error

### Implementation Details

#### Files Created:
- `EnrichedPaymentController.java` - REST controller
- `EnrichedPaymentService.java` - Business logic service
- `PurchaseWithDownloadAttempts.java` - DTO with download attempts array
- `EnrichedPurchaseResponse.java` - Response wrapper DTO
- `RazorpayPaymentData.java` - Razorpay payment data DTO

#### External Dependencies:
- **Payment Gateway Service**: `https://brainstrom-payment-gateway-158250499449.us-central1.run.app`
- **Local Database**: Firestore collections (download_attempt)

The implementation is complete and ready for testing!
