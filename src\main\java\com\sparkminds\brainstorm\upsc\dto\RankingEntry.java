package com.sparkminds.brainstorm.upsc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
// RankingEntry.java
public class RankingEntry {
    private Integer rank;
    private String name;
    private String email;
    private Double score;
    private Double totalMarks;
    private Double maxMarks;
    private Integer timeTaken;
    private Integer correctAnswers;
    private Integer incorrectAnswers;
    private Integer unanswered;
    private Double percentile;
    private String completedAt;
    // getters and setters
}
