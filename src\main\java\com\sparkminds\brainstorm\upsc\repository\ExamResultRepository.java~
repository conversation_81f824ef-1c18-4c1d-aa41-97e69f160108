package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.ExamResult;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.util.List;

@Repository
public interface ExamResultRepository extends FirestoreReactiveRepository<ExamResult> {
    Flux<ExamResult> findByUserId(String userId);

    Flux<ExamResult> findByExamId(String examId);

    List<ExamResult> findByExamIdOrderByTotalMarksDescTimeTakenMinutesAsc(String examId);
}