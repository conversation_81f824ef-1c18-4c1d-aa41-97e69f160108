package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Dailyquizquestion;
import com.sparkminds.brainstorm.upsc.service.DailyquizquestionService;

@RestController
@RequestMapping("/api/dailyQuizQuestions")
public class DailyquizquestionController {
    private final DailyquizquestionService service;
    
    public DailyquizquestionController(DailyquizquestionService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<Dailyquizquestion> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<Dailyquizquestion> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<Dailyquizquestion> create(@RequestBody Dailyquizquestion obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<Dailyquizquestion> update(@PathVariable String id, @RequestBody Dailyquizquestion obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}