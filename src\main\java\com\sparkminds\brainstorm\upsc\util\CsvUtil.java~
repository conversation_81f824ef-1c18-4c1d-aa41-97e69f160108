package com.sparkminds.brainstorm.upsc.util;

import com.sparkminds.brainstorm.upsc.model.Question;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

public class CsvUtil {
    private static final Logger log = LoggerFactory.getLogger(CsvUtil.class);
    private static final String[] EXPECTED_HEADERS = {
            "question", "option1", "option2", "option3", "option4", "correctAnswer", "explanation",
            "subject", "topic", "category", "difficulty", "marks", "negativeMarks", "questionType", "tags", "imageUrl"
    };
    private static final Pattern URL_PATTERN = Pattern.compile(
            "^(https?://)?([\\w-]+\\.)+[\\w-]+(/[\\w-./?%&=]*)?$"
    );

    public static List<Question> parseQuestionsFromCsv(InputStream inputStream, String examId) throws Exception {
        List<Question> questions = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {

            // Validate headers
            String[] headers = csvParser.getHeaderMap().keySet().toArray(new String[0]);
            if (!Arrays.equals(headers, EXPECTED_HEADERS)) {
                errors.add("Invalid CSV headers. Expected: " + Arrays.toString(EXPECTED_HEADERS));
                throw new IllegalArgumentException(String.join("; ", errors));
            }

            // Parse records
            for (CSVRecord record : csvParser) {
                try {
                    Question question = new Question();
                    question.setExamId(examId);
                    question.setStatus("active");

                    // Validate and set fields
                    String questionText = record.get("question").trim();
                    if (questionText.isEmpty()) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Question text is empty");
                        continue;
                    }
                    question.setQuestion(questionText);

                    String option1 = record.get("option1").trim();
                    String option2 = record.get("option2").trim();
                    String option3 = record.get("option3").trim();
                    String option4 = record.get("option4").trim();
                    String questionType = record.get("questionType").trim();

                    // Validate options based on questionType
                    List<String> options;
                    if (questionType.equals("true_false")) {
                        if (option1.isEmpty() || option2.isEmpty()) {
                            errors.add("Row " + (record.getRecordNumber() + 1) + ": option1 and option2 must be non-empty for true_false questions");
                            continue;
                        }
                        if (!option3.isEmpty() || !option4.isEmpty()) {
                            errors.add("Row " + (record.getRecordNumber() + 1) + ": option3 and option4 must be empty for true_false questions");
                            continue;
                        }
                        options = Arrays.asList(option1, option2);
                    } else if (questionType.equals("mcq") || questionType.equals("numerical")) {
                        if (option1.isEmpty() || option2.isEmpty() || option3.isEmpty() || option4.isEmpty()) {
                            errors.add("Row " + (record.getRecordNumber() + 1) + ": All four options (option1, option2, option3, option4) must be non-empty for mcq or numerical questions");
                            continue;
                        }
                        options = Arrays.asList(option1, option2, option3, option4);
                    } else {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Question type must be mcq, true_false, or numerical");
                        continue;
                    }
                    question.setOptions(options);

                    String correctAnswer = record.get("correctAnswer").trim();
                    if (correctAnswer.isEmpty()) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Correct answer is empty");
                        continue;
                    }
                    if (!options.contains(correctAnswer)) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Correct answer must be one of the options");
                        continue;
                    }
                    question.setCorrectAnswer(correctAnswer);

                    String explanation = record.get("explanation").trim();
                    if (explanation.isEmpty()) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Explanation is empty");
                        continue;
                    }
                    question.setExplanation(explanation);

                    String subject = record.get("subject").trim();
                    if (subject.isEmpty()) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Subject is empty");
                        continue;
                    }
                    question.setSubject(subject);

                    String topic = record.get("topic").trim();
                    if (topic.isEmpty()) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Topic is empty");
                        continue;
                    }
                    question.setTopic(topic);

                    String category = record.get("category").trim();
                    if (category.isEmpty()) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Category is empty");
                        continue;
                    }
                    question.setCategory(category);

                    String difficulty = record.get("difficulty").trim();
                    if (!List.of("Easy", "Medium", "Hard").contains(difficulty)) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Difficulty must be Easy, Medium, or Hard");
                        continue;
                    }
                    question.setDifficulty(difficulty);

                    String marksStr = record.get("marks").trim();
                    try {
                        int marks = Integer.parseInt(marksStr);
                        if (marks <= 0) {
                            errors.add("Row " + (record.getRecordNumber() + 1) + ": Marks must be positive");
                            continue;
                        }
                        question.setMarks(marks);
                    } catch (NumberFormatException e) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Invalid marks format");
                        continue;
                    }

                    String negativeMarksStr = record.get("negativeMarks").trim();
                    try {
                        // THIS IS THE CORRECTED LINE
                        double negativeMarks = Double.parseDouble(negativeMarksStr);
                        if (negativeMarks < 0) {
                            errors.add("Row " + (record.getRecordNumber() + 1) + ": Negative marks cannot be negative");
                            continue;
                        }
                        question.setNegativeMarks(negativeMarks);
                    } catch (NumberFormatException e) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Invalid negative marks format");
                        continue;
                    }

                    if (!List.of("mcq", "true_false", "numerical").contains(questionType)) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Question type must be mcq, true_false, or numerical");
                        continue;
                    }
                    question.setQuestionType(questionType);

                    String tagsStr = record.get("tags").trim();
                    List<String> tags = tagsStr.isEmpty() ? new ArrayList<>() : Arrays.asList(tagsStr.split(",", -1)).stream()
                            .map(String::trim).filter(tag -> !tag.isEmpty()).toList();
                    question.setTags(tags);

                    String imageUrl = record.get("imageUrl").trim();
                    if (!imageUrl.isEmpty() && !URL_PATTERN.matcher(imageUrl).matches()) {
                        errors.add("Row " + (record.getRecordNumber() + 1) + ": Invalid image URL");
                        continue;
                    }
                    question.setImageUrl(imageUrl.isEmpty() ? null : imageUrl);

                    questions.add(question);
                } catch (Exception e) {
                    errors.add("Row " + (record.getRecordNumber() + 1) + ": Error processing - " + e.getMessage());
                }
            }

            if (!errors.isEmpty()) {
                throw new IllegalArgumentException("CSV validation errors: " + String.join("; ", errors));
            }
        } catch (Exception e) {
            log.error("Failed to parse CSV: {}", e.getMessage());
            throw new IllegalArgumentException("Failed to parse CSV: " + e.getMessage());
        }

        return questions;
    }
}