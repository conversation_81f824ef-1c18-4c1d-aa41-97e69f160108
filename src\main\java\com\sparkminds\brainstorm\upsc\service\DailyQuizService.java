package com.sparkminds.brainstorm.upsc.service;

import com.google.cloud.firestore.Firestore;
import com.sparkminds.brainstorm.upsc.model.DailyQuiz;
import com.sparkminds.brainstorm.upsc.model.Question;
import com.sparkminds.brainstorm.upsc.repository.DailyQuizRepository;
import com.sparkminds.brainstorm.upsc.repository.QuestionRepository;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.ArrayList; // ADDED
import java.util.Date;
import java.util.List; // ADDED
import java.util.Map; // ADDED
import java.util.function.Function; // ADDED
import java.util.stream.Collectors; // ADDED

@Service
public class DailyQuizService {
    private static final Logger logger = LoggerFactory.getLogger(DailyQuizService.class);
    private static final int DAILY_QUIZ_QUESTION_COUNT = 10;

    private final DailyQuizRepository dailyQuizRepository;
    private final QuestionRepository questionRepository;
    private final Firestore firestore;

    public DailyQuizService(
            DailyQuizRepository dailyQuizRepository,
            QuestionRepository questionRepository,
            Firestore firestore) {
        this.dailyQuizRepository = dailyQuizRepository;
        this.questionRepository = questionRepository;
        this.firestore = firestore;
    }

    // ########## START OF CORRECTION ##########
    public Flux<Question> getQuestionsForDailyQuiz(LocalDate localDate) {
        return getOrCreateDailyQuiz(localDate)
                .flatMapMany(dailyQuiz -> {
                    List<String> orderedQuestionIds = dailyQuiz.getQuestionIds();
                    if (orderedQuestionIds == null || orderedQuestionIds.isEmpty()) {
                        logger.warn("Daily quiz {} for date {} has no questions.", dailyQuiz.getId(), localDate);
                        return Flux.empty();
                    }

                    // 1. Fetch all questions by their IDs. The result is an UNORDERED Flux.
                    return questionRepository.findAllById(orderedQuestionIds)
                            // 2. Collect the unordered questions into a List.
                            .collectList()
                            // 3. Once we have the list, sort it based on the original ID order.
                            .map(unorderedQuestions -> {
                                // Create a map for quick lookups: ID -> Question Object
                                Map<String, Question> questionMap = unorderedQuestions.stream()
                                        .collect(Collectors.toMap(Question::getId, Function.identity()));

                                // Re-build the list in the correct order.
                                List<Question> sortedQuestions = new ArrayList<>();
                                for (String id : orderedQuestionIds) {
                                    if (questionMap.containsKey(id)) {
                                        sortedQuestions.add(questionMap.get(id));
                                    }
                                }
                                return sortedQuestions;
                            })
                            // 4. Convert the now-sorted List back into a Flux to return.
                            .flatMapMany(Flux::fromIterable);
                });
    }
    // ########## END OF CORRECTION ##########

    private Mono<DailyQuiz> getOrCreateDailyQuiz(LocalDate localDate) {
        if (localDate == null) {
            return Mono.error(new IllegalArgumentException("Date cannot be null"));
        }

        Date startOfDay = FirestoreDateTimeUtil.convertToDate(localDate);
        Date startOfNextDay = FirestoreDateTimeUtil.convertToDate(localDate.plusDays(1));

        logger.info("Searching for quiz for date {} (between {} and {})", localDate, startOfDay, startOfNextDay);

        return dailyQuizRepository.findByDateGreaterThanEqualAndDateLessThan(startOfDay, startOfNextDay)
                .next()
                .switchIfEmpty(Mono.defer(() -> generateNewDailyQuizForDate(localDate)))
                .doOnSuccess(quiz -> {
                    if (quiz != null) {
                        logger.info("Successfully retrieved EXISTING quiz. ID: {}, Question IDs: {}, for date: {}", quiz.getId(), quiz.getQuestionIds(), localDate);
                    }
                })
                .doOnError(error -> logger.error("Failed to retrieve or create quiz for date {}: {}", localDate, error.getMessage()));
    }

    private Mono<DailyQuiz> generateNewDailyQuizForDate(LocalDate localDate) {
        logger.info("No quiz found for date {}. GENERATING a new one.", localDate);
        return questionRepository.findRandomQuestionIds(DAILY_QUIZ_QUESTION_COUNT, firestore)
                .collectList()
                .flatMap(questionIds -> {
                    if (questionIds.size() < DAILY_QUIZ_QUESTION_COUNT) {
                        return Mono.error(new RuntimeException("Not enough questions in the database to generate a daily quiz."));
                    }
                    DailyQuiz newQuiz = new DailyQuiz();
                    newQuiz.setDate(FirestoreDateTimeUtil.convertToDate(localDate));
                    newQuiz.setQuestionIds(questionIds);
                    return dailyQuizRepository.save(newQuiz)
                            .doOnSuccess(savedQuiz -> {
                                logger.info("New quiz GENERATED. ID: {}, Question IDs: {}, for date: {}", savedQuiz.getId(), savedQuiz.getQuestionIds(), localDate);
                            });
                });
    }
}