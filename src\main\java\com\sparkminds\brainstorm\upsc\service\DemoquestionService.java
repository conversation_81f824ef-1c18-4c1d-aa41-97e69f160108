package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.sparkminds.brainstorm.upsc.model.Demoquestion;
import com.sparkminds.brainstorm.upsc.repository.DemoquestionRepository;

@Service
public class DemoquestionService {
    private final DemoquestionRepository repository;

    public DemoquestionService(DemoquestionRepository repository) {
        this.repository = repository;
    }

    public Flux<Demoquestion> findAll() {
        return repository.findAll();
    }

    public Mono<Demoquestion> findById(String id) {
        return repository.findById(id);
    }

    public Mono<Demoquestion> save(Demoquestion obj) {
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }
}