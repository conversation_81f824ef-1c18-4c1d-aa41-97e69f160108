package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.model.Dailyquizsubmission;
import com.sparkminds.brainstorm.upsc.model.QuizSubmissionRequest;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import com.sparkminds.brainstorm.upsc.service.DailyquizsubmissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

@RestController
@RequestMapping("/api/daily-quiz-submissions")
public class DailyquizsubmissionController {
    private final DailyquizsubmissionService service;
    private static final Logger log = LoggerFactory.getLogger(DailyquizsubmissionController.class);

    public DailyquizsubmissionController(DailyquizsubmissionService service) {
        this.service = service;
    }

    @PostMapping("/submit")
    public Mono<Dailyquizsubmission> submit(@RequestBody QuizSubmissionRequest request) {
        // Get the authenticated user's ID from the reactive security context
        return SecurityUtils.getCurrentUserFirebaseUid()
                .flatMap(userId -> {
                    if (userId == null || userId.isEmpty()) {
                        return Mono.error(new IllegalStateException("User could not be authenticated."));
                    }
                    // Once we have the userId, we call the service method.
                    return service.submitQuiz(userId, request);
                });
    }


    @GetMapping
    public Flux<Dailyquizsubmission> getSubmissions(@RequestParam(required = false) String userId) {
        if (userId != null && !userId.isEmpty()) {
            log.info("Request received for submissions by userId: {}", userId);
            return service.findSubmissionsByUserId(userId);
        }
        log.info("Request received for all submissions.");
        return service.findAll();
    }

    // ADDED: The new endpoint you requested.
    // It checks if the current authenticated user has a submission for a specific quiz.
    @GetMapping("/by-quiz/{dailyQuizId}")
    public Mono<ResponseEntity<Object>> getSubmissionForQuiz(@PathVariable String dailyQuizId) {
        // Step 1: Extract the user ID from the token, just like in StudymaterialController.
        return SecurityUtils.getCurrentUserFirebaseUid()
                .flatMap(userId -> {
                    // Step 2: Call the service to find the specific submission.
                    return service.findSubmissionByUserAndQuiz(userId, dailyQuizId)
                            // Step 3 (Case A - Found): If a submission is found, wrap it in a 200 OK response.
                            .map(submission -> ResponseEntity.ok().body((Object)submission))
                            // Step 4 (Case B - Not Found): If no submission is found, return the custom response you requested.
                            // A 404 Not Found status is standard and tells the frontend that the resource doesn't exist.
                            // The frontend can interpret this as "Not Completed".
                            .switchIfEmpty(Mono.fromCallable(() -> {
                                log.info("No submission found for user {} and quiz {}. Returning status.", userId, dailyQuizId);
                                // The user requested a "Quiz Completed" message, but it's more logical to say "Not Completed".
                                // Returning a simple JSON object is clean.
                                return ResponseEntity.ok().body(Map.of("status", "not_completed"));
                            }));
                })
                .onErrorResume(e -> Mono.just(ResponseEntity.status(401).body(Map.of("error", "Authentication failed."))));
    }

    @GetMapping("/{id}")
    public Mono<Dailyquizsubmission> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @PutMapping("/{id}")
    public Mono<Dailyquizsubmission> update(@PathVariable String id, @RequestBody Dailyquizsubmission obj) {
        obj.setId(id);
        return service.save(obj);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }
}