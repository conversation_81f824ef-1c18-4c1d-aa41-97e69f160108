package com.sparkminds.brainstorm.upsc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
// DetailedAnswer.java
public class DetailedAnswer {
    private String questionId;
    private String question;
    private List<String> options;
    private Integer correctAnswer;
    private Integer userAnswer;
    private Boolean isCorrect;
    private Boolean isAttempted;
    private Double marks;
    private String subject;
    private String difficulty;
    // getters and setters
}
