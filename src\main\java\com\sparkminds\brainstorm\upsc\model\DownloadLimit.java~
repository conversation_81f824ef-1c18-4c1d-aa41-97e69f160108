package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;

@Document(collectionName = "download_limit")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DownloadLimit  {

    @DocumentId
    private String id;
    private String userId;
    private String materialId;
    private Integer maxDownloads;
    private Integer usedDownloads;
    private Object lastDownloadAt;
    private Object resetDate;

    public LocalDateTime getLastDownloadAtAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(lastDownloadAt);
    }
    public LocalDateTime getResetDateAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(resetDate);
    }
}
