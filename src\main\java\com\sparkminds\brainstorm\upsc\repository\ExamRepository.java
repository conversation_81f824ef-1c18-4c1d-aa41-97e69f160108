package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.firestore.Query;
import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.Exam;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;

public interface ExamRepository extends FirestoreReactiveRepository<Exam> {
    Flux<Exam> findByStatus(String status);
    Flux<Exam> findByCategory(String category);
    Flux<Exam> findBySubject(String subject);
    Flux<Exam> findByIsPremium(Integer isPremium);
    Flux<Exam> findByStartTimeGreaterThanEqualAndStartTimeLessThanEqual(LocalDateTime from, LocalDateTime to);

    Flux<Exam> findTop4ByStatusAndStartTimeGreaterThanOrderByStartTimeAsc(String status, LocalDateTime startTime);
}