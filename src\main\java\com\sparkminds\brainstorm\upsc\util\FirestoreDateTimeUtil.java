package com.sparkminds.brainstorm.upsc.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;

public class FirestoreDateTimeUtil {
    public static LocalDateTime convertToLocalDateTime(Object obj) {
        if (obj == null) return null;
        if (obj instanceof LocalDateTime) return (LocalDateTime) obj;
        if (obj instanceof String) {
            String str = (String) obj;
            try {
                return LocalDateTime.parse(str);
            } catch (Exception e) {
                try {
                    return LocalDate.parse(str).atStartOfDay();
                } catch (Exception ex) {
                    return null;
                }
            }
        }
        if (obj instanceof Map) {
            Map map = (Map) obj;
            if (map.containsKey("year") && map.containsKey("monthValue") && map.containsKey("dayOfMonth")) {
                int year = ((Number) map.get("year")).intValue();
                int month = ((Number) map.get("monthValue")).intValue();
                int day = ((Number) map.get("dayOfMonth")).intValue();
                int hour = map.containsKey("hour") ? ((Number) map.get("hour")).intValue() : 0;
                int minute = map.containsKey("minute") ? ((Number) map.get("minute")).intValue() : 0;
                int second = map.containsKey("second") ? ((Number) map.get("second")).intValue() : 0;
                int nano = map.containsKey("nano") ? ((Number) map.get("nano")).intValue() : 0;
                return LocalDateTime.of(year, month, day, hour, minute, second, nano);
            }
            if (map.containsKey("_seconds")) {
                long seconds = ((Number) map.get("_seconds")).longValue();
                long nanos = map.containsKey("_nanoseconds") ? ((Number) map.get("_nanoseconds")).longValue() : 0L;
                return LocalDateTime.ofEpochSecond(seconds, (int) nanos, java.time.ZoneOffset.UTC);
            }
        }
        return null;
    }

    public static Date convertToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) return null;
        return Date.from(localDateTime.atZone(ZoneId.of("UTC")).toInstant());
    }

    public static Date convertToDate(LocalDate localDate) {
        if (localDate == null) return null;
        return Date.from(localDate.atStartOfDay(ZoneId.of("UTC")).toInstant());
    }

}
