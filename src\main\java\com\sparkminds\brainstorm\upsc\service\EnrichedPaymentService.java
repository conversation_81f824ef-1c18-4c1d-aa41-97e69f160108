package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.dto.EnrichedPurchaseResponse;
import com.sparkminds.brainstorm.upsc.dto.PurchaseWithDownloadAttempts;
import com.sparkminds.brainstorm.upsc.model.DownloadAttempt;
import com.sparkminds.brainstorm.upsc.repository.DownloadAttemptRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

@Service
public class EnrichedPaymentService {
    private static final Logger log = LoggerFactory.getLogger(EnrichedPaymentService.class);
    
    private final WebClient paymentServiceWebClient;
    private final DownloadAttemptRepository downloadAttemptRepository;
    
    public EnrichedPaymentService(WebClient paymentServiceWebClient, 
                                 DownloadAttemptRepository downloadAttemptRepository) {
        this.paymentServiceWebClient = paymentServiceWebClient;
        this.downloadAttemptRepository = downloadAttemptRepository;
    }
    
    public Mono<EnrichedPurchaseResponse> getEnrichedPurchases(
            Long from, Long to, Integer count, Integer skip,
            Integer limit, Integer offset,
            String userId, String status, Boolean includeRazorpayData) {
        
        log.info("Fetching enriched purchases with parameters - from: {}, to: {}, count: {}, skip: {}, limit: {}, offset: {}, userId: {}, status: {}, includeRazorpayData: {}", 
                from, to, count, skip, limit, offset, userId, status, includeRazorpayData);
        
        // Set default to today's data if no date parameters provided
        Long finalFrom = from;
        Long finalTo = to;
        
        if (from == null && to == null && count == null && skip == null && limit == null && offset == null) {
            // Default to today's data
            LocalDate today = LocalDate.now();
            LocalDateTime startOfDay = today.atStartOfDay();
            LocalDateTime endOfDay = today.atTime(23, 59, 59);
            
            finalFrom = startOfDay.toEpochSecond(ZoneOffset.UTC);
            finalTo = endOfDay.toEpochSecond(ZoneOffset.UTC);
            
            log.info("No parameters provided, defaulting to today's data: from={}, to={}", finalFrom, finalTo);
        }
        
        // Fetch data from external payment gateway service
        return fetchFromPaymentGateway(finalFrom, finalTo, count, skip, limit, offset, userId, status, includeRazorpayData)
                .flatMap(this::enrichWithDownloadAttempts);
    }
    
    private Mono<EnrichedPurchaseResponse> fetchFromPaymentGateway(
            Long from, Long to, Integer count, Integer skip,
            Integer limit, Integer offset,
            String userId, String status, Boolean includeRazorpayData) {
        
        return paymentServiceWebClient.get()
                .uri(uriBuilder -> {
                    var builder = uriBuilder.path("/api/payments/purchases/all");
                    
                    if (from != null) builder.queryParam("from", from);
                    if (to != null) builder.queryParam("to", to);
                    if (count != null) builder.queryParam("count", count);
                    if (skip != null) builder.queryParam("skip", skip);
                    if (limit != null) builder.queryParam("limit", limit);
                    if (offset != null) builder.queryParam("offset", offset);
                    if (userId != null) builder.queryParam("userId", userId);
                    if (status != null) builder.queryParam("status", status);
                    if (includeRazorpayData != null) builder.queryParam("includeRazorpayData", includeRazorpayData);
                    
                    return builder.build();
                })
                .retrieve()
                .bodyToMono(EnrichedPurchaseResponse.class)
                .doOnError(error -> log.error("Failed to fetch data from payment gateway: {}", error.getMessage()))
                .onErrorResume(error -> {
                    log.error("Error fetching from payment gateway, returning empty response", error);
                    EnrichedPurchaseResponse emptyResponse = new EnrichedPurchaseResponse();
                    emptyResponse.setEntity("collection");
                    emptyResponse.setTotalCount(0L);
                    emptyResponse.setReturnedCount(0);
                    emptyResponse.setOffset(offset != null ? offset : (skip != null ? skip : 0));
                    emptyResponse.setLimit(limit != null ? limit : (count != null ? count : 10));
                    emptyResponse.setHasMore(false);
                    emptyResponse.setRazorpayDataFoundCount(0);
                    emptyResponse.setRazorpayDataMissingCount(0);
                    emptyResponse.setItems(List.of());
                    return Mono.just(emptyResponse);
                });
    }
    
    private Mono<EnrichedPurchaseResponse> enrichWithDownloadAttempts(EnrichedPurchaseResponse response) {
        if (response.getItems() == null || response.getItems().isEmpty()) {
            log.info("No items to enrich, returning original response");
            return Mono.just(response);
        }
        
        log.info("Enriching {} purchases with download attempts", response.getItems().size());
        
        return Flux.fromIterable(response.getItems())
                .flatMap(this::enrichSinglePurchaseWithDownloadAttempts)
                .collectList()
                .map(enrichedItems -> {
                    response.setItems(enrichedItems);
                    return response;
                });
    }
    
    private Mono<PurchaseWithDownloadAttempts> enrichSinglePurchaseWithDownloadAttempts(PurchaseWithDownloadAttempts purchase) {
        if (purchase.getUserId() == null || purchase.getMaterialId() == null) {
            log.warn("Purchase {} missing userId or materialId, skipping download attempts enrichment", purchase.getId());
            purchase.setDownloadAttempts(List.of());
            return Mono.just(purchase);
        }
        
        return downloadAttemptRepository.findByUserId(purchase.getUserId())
                .filter(attempt -> purchase.getMaterialId().equals(attempt.getMaterialId()))
                .collectList()
                .map(downloadAttempts -> {
                    log.debug("Found {} download attempts for purchase {} (userId: {}, materialId: {})", 
                            downloadAttempts.size(), purchase.getId(), purchase.getUserId(), purchase.getMaterialId());
                    purchase.setDownloadAttempts(downloadAttempts);
                    return purchase;
                })
                .onErrorResume(error -> {
                    log.error("Error fetching download attempts for purchase {}: {}", purchase.getId(), error.getMessage());
                    purchase.setDownloadAttempts(List.of());
                    return Mono.just(purchase);
                });
    }
}
