<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CopilotDiffPersistence">
    <option name="pendingDiffs">
      <map>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/config/SecurityConfig.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/config/SecurityConfig.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.config;&#10;&#10;import com.sparkminds.brainstorm.upsc.security.FirebaseAuthenticationFilter;&#10;import org.springframework.context.annotation.Bean;&#10;import org.springframework.context.annotation.Configuration;&#10;import org.springframework.http.HttpMethod;&#10;import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;&#10;import org.springframework.security.config.web.server.ServerHttpSecurity;&#10;import org.springframework.security.web.server.SecurityWebFilterChain;&#10;import org.springframework.web.cors.CorsConfiguration;&#10;import org.springframework.web.server.ServerWebExchange;&#10;import org.springframework.web.server.WebFilter;&#10;import org.springframework.web.server.WebFilterChain;&#10;import reactor.core.publisher.Mono;&#10;&#10;import static org.springframework.security.config.web.server.SecurityWebFiltersOrder.AUTHENTICATION;&#10;&#10;@Configuration&#10;@EnableWebFluxSecurity&#10;public class SecurityConfig {&#10;&#10;    @Bean&#10;    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {&#10;        http&#10;            .csrf(ServerHttpSecurity.CsrfSpec::disable)&#10;            .cors(cors -&gt; cors.configurationSource(exchange -&gt; {&#10;                CorsConfiguration config = new CorsConfiguration();&#10;                config.addAllowedOrigin(&quot;*&quot;);&#10;                config.addAllowedMethod(&quot;*&quot;);&#10;                config.addAllowedHeader(&quot;*&quot;);&#10;                return config;&#10;            }))&#10;            .authorizeExchange(authorize -&gt; authorize&#10;                // Allow all OPTIONS requests for CORS preflight&#10;                .pathMatchers(HttpMethod.OPTIONS, &quot;/**&quot;).permitAll()&#10;&#10;                // Public Endpoints (NO Authentication Required)&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/testimonials&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/pricingPlans&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/landingStats&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/landingFeatures&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/newsItems&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/demoQuestions&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/dailyQuizQuestions&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/questions&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/auth/login&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/auth/register&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/health&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/swagger-ui.html&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/v3/api-docs&quot;).permitAll()&#10;                // Public Exam Information (Limited)&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exams/1&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/users/me&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/userProfiles/me&quot;).permitAll()&#10;&#10;                // Protected Endpoints (Firebase Authentication Required)&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/users/{userId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.PUT, &quot;/api/users/{userId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/userProfiles&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/auth/validate&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exams&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exams/{examId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/questions&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/exam-results&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exam-results/{resultId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exam-results/ranking/{examId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exam-results/live-ranking/{examId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exam-results/{resultId}/ranking&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/studyMaterials&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/dailyQuizQuestions&quot;).authenticated()&#10;                // ADMIN-ONLY ENDPOINTS (Require ADMIN Role)&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/admin/**&quot;).hasRole(&quot;ADMIN&quot;)&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/admin/**&quot;).hasRole(&quot;ADMIN&quot;)&#10;                .pathMatchers(HttpMethod.PUT, &quot;/api/admin/**&quot;).hasRole(&quot;ADMIN&quot;)&#10;                .pathMatchers(HttpMethod.DELETE, &quot;/api/admin/**&quot;).hasRole(&quot;ADMIN&quot;)&#10;&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/progressData&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/upcomingExams&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/recentResults&quot;).authenticated()&#10;                // All other endpoints require authentication&#10;                .anyExchange().hasRole(&quot;ADMIN&quot;)&#10;            )&#10;            .addFilterBefore(requestLoggingFilter(), AUTHENTICATION);&#10;&#10;        return http.build();&#10;    }&#10;&#10;    // WebFilter to log request method, path, and headers&#10;    @Bean&#10;    public WebFilter requestLoggingFilter() {&#10;        return (exchange, chain) -&gt; {&#10;            System.out.println(&quot;Incoming request: &quot; + exchange.getRequest().getMethod() + &quot; &quot; + exchange.getRequest().getPath());&#10;            String authHeader = exchange.getRequest().getHeaders().getFirst(&quot;Authorization&quot;);&#10;            System.out.println(&quot;Authorization header: &quot; + authHeader);&#10;            return chain.filter(exchange)&#10;                .doOnSuccessOrError((resp, ex) -&gt; {&#10;                    Integer status = exchange.getResponse().getStatusCode() != null ? exchange.getResponse().getStatusCode().value() : null;&#10;                    System.out.println(&quot;[SECURITY CONFIG] Response status: &quot; + status);&#10;                });&#10;        };&#10;    }&#10;}" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.config;&#10;&#10;import com.sparkminds.brainstorm.upsc.security.FirebaseAuthenticationFilter;&#10;import org.springframework.context.annotation.Bean;&#10;import org.springframework.context.annotation.Configuration;&#10;import org.springframework.http.HttpMethod;&#10;import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;&#10;import org.springframework.security.config.web.server.ServerHttpSecurity;&#10;import org.springframework.security.web.server.SecurityWebFilterChain;&#10;import org.springframework.web.cors.CorsConfiguration;&#10;import org.springframework.web.server.ServerWebExchange;&#10;import org.springframework.web.server.WebFilter;&#10;import org.springframework.web.server.WebFilterChain;&#10;import reactor.core.publisher.Mono;&#10;&#10;import static org.springframework.security.config.web.server.SecurityWebFiltersOrder.AUTHENTICATION;&#10;&#10;@Configuration&#10;@EnableWebFluxSecurity&#10;public class SecurityConfig {&#10;&#10;    @Bean&#10;    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {&#10;        http&#10;            .csrf(ServerHttpSecurity.CsrfSpec::disable)&#10;            .cors(cors -&gt; cors.configurationSource(exchange -&gt; {&#10;                CorsConfiguration config = new CorsConfiguration();&#10;                config.addAllowedOrigin(&quot;*&quot;);&#10;                config.addAllowedMethod(&quot;*&quot;);&#10;                config.addAllowedHeader(&quot;*&quot;);&#10;                return config;&#10;            }))&#10;            .authorizeExchange(authorize -&gt; authorize&#10;                // Allow all OPTIONS requests for CORS preflight&#10;                .pathMatchers(HttpMethod.OPTIONS, &quot;/**&quot;).permitAll()&#10;&#10;                // Public Endpoints (NO Authentication Required)&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/testimonials&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/pricingPlans&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/landingStats&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/landingFeatures&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/newsItems&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/demoQuestions&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/dailyQuizQuestions&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/questions&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/auth/login&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/auth/register&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/health&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/swagger-ui.html&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/v3/api-docs&quot;).permitAll()&#10;                // Public Exam Information (Limited)&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exams/1&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/users/me&quot;).permitAll()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/userProfiles/me&quot;).permitAll()&#10;&#10;                // Protected Endpoints (Firebase Authentication Required)&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/users/{userId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.PUT, &quot;/api/users/{userId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/userProfiles&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/auth/validate&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exams&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exams/{examId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/questions&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/exam-results&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exam-results/{resultId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exam-results/ranking/{examId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exam-results/live-ranking/{examId}&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/exam-results/{resultId}/ranking&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/studyMaterials&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/dailyQuizQuestions&quot;).authenticated()&#10;                // ADMIN-ONLY ENDPOINTS (Require ADMIN Role)&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/admin/**&quot;).hasRole(&quot;ADMIN&quot;)&#10;                .pathMatchers(HttpMethod.POST, &quot;/api/admin/**&quot;).hasRole(&quot;ADMIN&quot;)&#10;                .pathMatchers(HttpMethod.PUT, &quot;/api/admin/**&quot;).hasRole(&quot;ADMIN&quot;)&#10;                .pathMatchers(HttpMethod.DELETE, &quot;/api/admin/**&quot;).hasRole(&quot;ADMIN&quot;)&#10;&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/progressData&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/upcomingExams&quot;).authenticated()&#10;                .pathMatchers(HttpMethod.GET, &quot;/api/recentResults&quot;).authenticated()&#10;                // All other endpoints require authentication&#10;                .anyExchange().hasRole(&quot;ADMIN&quot;)&#10;            )&#10;            .addFilterBefore(requestLoggingFilter(), AUTHENTICATION);&#10;&#10;        return http.build();&#10;    }&#10;&#10;    // WebFilter to log request method, path, and headers&#10;    @Bean&#10;    public WebFilter requestLoggingFilter() {&#10;        return (exchange, chain) -&gt; {&#10;            System.out.println(&quot;Incoming request: &quot; + exchange.getRequest().getMethod() + &quot; &quot; + exchange.getRequest().getPath());&#10;            String authHeader = exchange.getRequest().getHeaders().getFirst(&quot;Authorization&quot;);&#10;            System.out.println(&quot;Authorization header: &quot; + authHeader);&#10;            return chain.filter(exchange)&#10;                .doOnTerminate(() -&gt; {&#10;                    Integer status = exchange.getResponse().getStatusCode() != null ? exchange.getResponse().getStatusCode().value() : null;&#10;                    System.out.println(&quot;[SECURITY CONFIG] Response status: &quot; + status);&#10;                });&#10;        };&#10;    }&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/controller/ExamResultController.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/controller/ExamResultController.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.controller;&#10;&#10;import org.springframework.web.bind.annotation.*;&#10;import reactor.core.publisher.Flux;&#10;import reactor.core.publisher.Mono;&#10;import com.sparkminds.brainstorm.upsc.model.ExamResult;&#10;import com.sparkminds.brainstorm.upsc.service.ExamResultService;&#10;import com.sparkminds.brainstorm.upsc.dto.*;&#10;import org.springframework.http.ResponseEntity;&#10;&#10;@RestController&#10;@RequestMapping(&quot;/api/exam-results&quot;)&#10;public class ExamResultController {&#10;    private final ExamResultService service;&#10;&#10;    public ExamResultController(ExamResultService service) {&#10;        this.service = service;&#10;    }&#10;&#10;    @GetMapping&#10;    public Flux&lt;ExamResult&gt; getAll() {&#10;        return service.findAll();&#10;    }&#10;&#10;    @GetMapping(&quot;/{id}&quot;)&#10;    public Mono&lt;ExamResult&gt; getById(@PathVariable String id) {&#10;        return service.findById(id);&#10;    }&#10;&#10;    @GetMapping(&quot;/user/{userId}&quot;)&#10;    public Flux&lt;ExamResult&gt; getByUserId(@PathVariable String userId) {&#10;        return service.findByUserId(userId);&#10;    }&#10;&#10;    @GetMapping(&quot;/exam/{examId}&quot;)&#10;    public Flux&lt;ExamResult&gt; getByExamId(@PathVariable String examId) {&#10;        return service.findByExamId(examId);&#10;    }&#10;&#10;    @PostMapping&#10;    public Mono&lt;ExamResult&gt; create(@RequestBody ExamResult examResult) {&#10;        return service.save(examResult);&#10;    }&#10;&#10;    @PutMapping(&quot;/{id}&quot;)&#10;    public Mono&lt;ExamResult&gt; update(@PathVariable String id, @RequestBody ExamResult examResult) {&#10;        examResult.setId(id);&#10;        return service.save(examResult);&#10;    }&#10;&#10;    @DeleteMapping(&quot;/{id}&quot;)&#10;    public Mono&lt;Void&gt; delete(@PathVariable String id) {&#10;        return service.delete(id);&#10;    }&#10;&#10;    // --- Replace or add endpoints to match frontend expectations ---&#10;&#10;    // 1. GET /api/exam-results/ranking/{examId}&#10;    @GetMapping(&quot;/ranking/{examId}&quot;)&#10;    public Mono&lt;ResponseEntity&lt;ExamRankingResponse&gt;&gt; getExamRanking(@PathVariable String examId) {&#10;        return service.getExamRanking(examId)&#10;                .map(ResponseEntity::ok)&#10;                .defaultIfEmpty(ResponseEntity.notFound().build());&#10;    }&#10;&#10;    // 2. GET /api/exam-results/live-ranking/{examId}&#10;    @GetMapping(&quot;/live-ranking/{examId}&quot;)&#10;    public Mono&lt;ResponseEntity&lt;LiveRankingResponse&gt;&gt; getLiveRanking(@PathVariable String examId) {&#10;        // TODO: Implement real live ranking logic if needed&#10;        return Mono.just(ResponseEntity.ok(new LiveRankingResponse()));&#10;    }&#10;&#10;    // 3. GET /api/exam-results/{resultId}&#10;    @GetMapping(&quot;/{resultId}&quot;)&#10;    public Mono&lt;ResponseEntity&lt;ExamResultResponse&gt;&gt; getExamResult(@PathVariable String resultId) {&#10;        return service.findById(resultId)&#10;                .map(service::convertToResponse)&#10;                .map(ResponseEntity::ok)&#10;                .defaultIfEmpty(ResponseEntity.notFound().build());&#10;    }&#10;&#10;    // 4. GET /api/exam-results/{resultId}/ranking&#10;    @GetMapping(&quot;/{resultId}/ranking&quot;)&#10;    public Mono&lt;ResponseEntity&lt;RankingStatistics&gt;&gt; getResultRanking(@PathVariable String resultId) {&#10;        return service.findById(resultId)&#10;                .flatMap(result -&gt; service.findByExamId(result.getExamId()).collectList())&#10;                .map(service::calculateStatistics)&#10;                .map(ResponseEntity::ok)&#10;                .defaultIfEmpty(ResponseEntity.notFound().build());&#10;    }&#10;&#10;    // 5. POST /api/exam-results&#10;    @PostMapping&#10;    public Mono&lt;ResponseEntity&lt;ExamResultResponse&gt;&gt; submitExamResult(@RequestBody ExamResultRequest request) {&#10;        return service.submitExamResult(request)&#10;                .map(ResponseEntity::ok)&#10;                .defaultIfEmpty(ResponseEntity.badRequest().build());&#10;    }&#10;}" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.controller;&#10;&#10;import org.springframework.web.bind.annotation.*;&#10;import reactor.core.publisher.Flux;&#10;import reactor.core.publisher.Mono;&#10;import com.sparkminds.brainstorm.upsc.model.ExamResult;&#10;import com.sparkminds.brainstorm.upsc.service.ExamResultService;&#10;import com.sparkminds.brainstorm.upsc.dto.*;&#10;import org.springframework.http.ResponseEntity;&#10;&#10;@RestController&#10;@RequestMapping(&quot;/api/exam-results&quot;)&#10;public class ExamResultController {&#10;    private final ExamResultService service;&#10;&#10;    public ExamResultController(ExamResultService service) {&#10;        this.service = service;&#10;    }&#10;&#10;    @GetMapping&#10;    public Flux&lt;ExamResult&gt; getAll() {&#10;        return service.findAll();&#10;    }&#10;&#10;    @GetMapping(&quot;/{id}&quot;)&#10;    public Mono&lt;ExamResult&gt; getById(@PathVariable String id) {&#10;        return service.findById(id);&#10;    }&#10;&#10;    @GetMapping(&quot;/user/{userId}&quot;)&#10;    public Flux&lt;ExamResult&gt; getByUserId(@PathVariable String userId) {&#10;        return service.findByUserId(userId);&#10;    }&#10;&#10;    @GetMapping(&quot;/exam/{examId}&quot;)&#10;    public Flux&lt;ExamResult&gt; getByExamId(@PathVariable String examId) {&#10;        return service.findByExamId(examId);&#10;    }&#10;&#10;    // Removed: create(ExamResult) to resolve mapping conflict&#10;    // @PostMapping&#10;    // public Mono&lt;ExamResult&gt; create(@RequestBody ExamResult examResult) {&#10;    //     return service.save(examResult);&#10;    // }&#10;&#10;    @PutMapping(&quot;/{id}&quot;)&#10;    public Mono&lt;ExamResult&gt; update(@PathVariable String id, @RequestBody ExamResult examResult) {&#10;        examResult.setId(id);&#10;        return service.save(examResult);&#10;    }&#10;&#10;    @DeleteMapping(&quot;/{id}&quot;)&#10;    public Mono&lt;Void&gt; delete(@PathVariable String id) {&#10;        return service.delete(id);&#10;    }&#10;&#10;    // --- Replace or add endpoints to match frontend expectations ---&#10;&#10;    // 1. GET /api/exam-results/ranking/{examId}&#10;    @GetMapping(&quot;/ranking/{examId}&quot;)&#10;    public Mono&lt;ResponseEntity&lt;ExamRankingResponse&gt;&gt; getExamRanking(@PathVariable String examId) {&#10;        return service.getExamRanking(examId)&#10;                .map(ResponseEntity::ok)&#10;                .defaultIfEmpty(ResponseEntity.notFound().build());&#10;    }&#10;&#10;    // 2. GET /api/exam-results/live-ranking/{examId}&#10;    @GetMapping(&quot;/live-ranking/{examId}&quot;)&#10;    public Mono&lt;ResponseEntity&lt;LiveRankingResponse&gt;&gt; getLiveRanking(@PathVariable String examId) {&#10;        // TODO: Implement real live ranking logic if needed&#10;        return Mono.just(ResponseEntity.ok(new LiveRankingResponse()));&#10;    }&#10;&#10;    // 3. GET /api/exam-results/{resultId}&#10;    @GetMapping(&quot;/{resultId}&quot;)&#10;    public Mono&lt;ResponseEntity&lt;ExamResultResponse&gt;&gt; getExamResult(@PathVariable String resultId) {&#10;        return service.findById(resultId)&#10;                .map(service::convertToResponse)&#10;                .map(ResponseEntity::ok)&#10;                .defaultIfEmpty(ResponseEntity.notFound().build());&#10;    }&#10;&#10;    // 4. GET /api/exam-results/{resultId}/ranking&#10;    @GetMapping(&quot;/{resultId}/ranking&quot;)&#10;    public Mono&lt;ResponseEntity&lt;RankingStatistics&gt;&gt; getResultRanking(@PathVariable String resultId) {&#10;        return service.findById(resultId)&#10;                .flatMap(result -&gt; service.findByExamId(result.getExamId()).collectList())&#10;                .map(service::calculateStatistics)&#10;                .map(ResponseEntity::ok)&#10;                .defaultIfEmpty(ResponseEntity.notFound().build());&#10;    }&#10;&#10;    // 5. POST /api/exam-results&#10;    @PostMapping&#10;    public Mono&lt;ResponseEntity&lt;ExamResultResponse&gt;&gt; submitExamResult(@RequestBody ExamResultRequest request) {&#10;        return service.submitExamResult(request)&#10;                .map(ResponseEntity::ok)&#10;                .defaultIfEmpty(ResponseEntity.badRequest().build());&#10;    }&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/dto/ExamResultResponse.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/dto/ExamResultResponse.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.dto;&#10;&#10;import com.sparkminds.brainstorm.upsc.model.ExamResult;&#10;import lombok.AllArgsConstructor;&#10;import lombok.Builder;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;@Builder&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class ExamResultResponse extends ExamResult {&#10;    // Add fields here as needed&#10;}&#10;" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.dto;&#10;&#10;import lombok.AllArgsConstructor;&#10;import lombok.Builder;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;@Builder&#10;@Data&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class ExamResultResponse {&#10;    private String examId;&#10;    private String userId;&#10;    private String examTitle;&#10;    private Integer score;&#10;    private Integer totalQuestions;&#10;    private Integer totalMarks;&#10;    private Integer correctAnswers;&#10;    private Integer incorrectAnswers;&#10;    private Integer unanswered;&#10;    private String timeTakenMinutes;&#10;    private Integer rank;&#10;    private Double percentile;&#10;    private java.util.List&lt;com.sparkminds.brainstorm.upsc.model.ExamResult.SubjectResult&gt; subjects;&#10;    private String completedAt;&#10;    private String submittedAt;&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/exception/GlobalExceptionHandler.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/exception/GlobalExceptionHandler.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.exception;&#10;&#10;import com.sparkminds.brainstorm.upsc.dto.ApiResponse;&#10;import org.springframework.http.HttpStatus;&#10;import org.springframework.http.ResponseEntity;&#10;import org.springframework.validation.FieldError;&#10;import org.springframework.web.bind.MethodArgumentNotValidException;&#10;import org.springframework.web.bind.annotation.ExceptionHandler;&#10;import org.springframework.web.bind.annotation.RestControllerAdvice;&#10;import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;&#10;import org.springframework.web.server.ServerWebExchange;&#10;import org.springframework.http.converter.HttpMessageNotReadableException;&#10;&#10;import java.util.HashMap;&#10;import java.util.Map;&#10;&#10;@RestControllerAdvice&#10;public class GlobalExceptionHandler {&#10;    &#10;    @ExceptionHandler(ResourceNotFoundException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleResourceNotFoundException(&#10;            ResourceNotFoundException ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.NOT_FOUND);&#10;    }&#10;    &#10;    @ExceptionHandler(BadRequestException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleBadRequestException(&#10;            BadRequestException ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;    &#10;    @ExceptionHandler(UnauthorizedException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleUnauthorizedException(&#10;            UnauthorizedException ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.UNAUTHORIZED);&#10;    }&#10;    &#10;    @ExceptionHandler(MethodArgumentNotValidException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleValidationExceptions(&#10;            MethodArgumentNotValidException ex) {&#10;        &#10;        Map&lt;String, String&gt; errors = new HashMap&lt;&gt;();&#10;        ex.getBindingResult().getAllErrors().forEach((error) -&gt; {&#10;            String fieldName = ((FieldError) error).getField();&#10;            String errorMessage = error.getDefaultMessage();&#10;            errors.put(fieldName, errorMessage);&#10;        });&#10;        &#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;Validation failed&quot;, errors.toString());&#10;        response.setData(errors);&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;    &#10;    @ExceptionHandler(MethodArgumentTypeMismatchException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleTypeMismatchException(&#10;            MethodArgumentTypeMismatchException ex, ServerWebExchange exchange) {&#10;&#10;        String message = String.format(&quot;Invalid value '%s' for parameter '%s'. Expected type: %s&quot;,&#10;                ex.getValue(), ex.getName(), ex.getRequiredType().getSimpleName());&#10;        &#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(message);&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;    &#10;    @ExceptionHandler(IllegalArgumentException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleIllegalArgumentException(&#10;            IllegalArgumentException ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;    &#10;    @ExceptionHandler(RuntimeException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleRuntimeException(&#10;            RuntimeException ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;An error occurred: &quot; + ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.INTERNAL_SERVER_ERROR);&#10;    }&#10;    &#10;    @ExceptionHandler(Exception.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleGlobalException(&#10;            Exception ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;Internal server error occurred&quot;);&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.INTERNAL_SERVER_ERROR);&#10;    }&#10;&#10;    @ExceptionHandler(HttpMessageNotReadableException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleHttpMessageNotReadableException(&#10;            HttpMessageNotReadableException ex, ServerWebExchange exchange) {&#10;        String expectedSchema = &quot;{\n  id: String,\n  title: String,\n  description: String,\n  type: String,\n  subject: String,\n  price: Integer,\n  originalPrice: Integer,\n  rating: Double,\n  reviews: Integer,\n  pages: Integer,\n  author: String,\n  isPremium: Integer,\n  thumbnailUrl: String,\n  status: String,\n  downloads: Integer,\n  createdAt: String (ISO 8601),\n  fileSize: String,\n  filePath: String,\n  originalName: String\n}&quot;;&#10;        String message = &quot;Malformed JSON or type mismatch: &quot; + ex.getMessage();&#10;        Map&lt;String, Object&gt; details = new HashMap&lt;&gt;();&#10;        details.put(&quot;expectedSchema&quot;, expectedSchema);&#10;        details.put(&quot;error&quot;, message);&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;Invalid request body&quot;, details.toString());&#10;        response.setData(details);&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;}&#10;" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.exception;&#10;&#10;import com.sparkminds.brainstorm.upsc.dto.ApiResponse;&#10;import org.springframework.http.HttpStatus;&#10;import org.springframework.http.ResponseEntity;&#10;import org.springframework.validation.FieldError;&#10;import org.springframework.web.bind.MethodArgumentNotValidException;&#10;import org.springframework.web.bind.annotation.ExceptionHandler;&#10;import org.springframework.web.bind.annotation.RestControllerAdvice;&#10;import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;&#10;import org.springframework.web.server.ServerWebExchange;&#10;import org.springframework.http.converter.HttpMessageNotReadableException;&#10;&#10;import java.util.HashMap;&#10;import java.util.Map;&#10;&#10;@RestControllerAdvice&#10;public class GlobalExceptionHandler {&#10;    &#10;    @ExceptionHandler(ResourceNotFoundException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleResourceNotFoundException(&#10;            ResourceNotFoundException ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.NOT_FOUND);&#10;    }&#10;    &#10;    @ExceptionHandler(BadRequestException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleBadRequestException(&#10;            BadRequestException ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;    &#10;    @ExceptionHandler(UnauthorizedException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleUnauthorizedException(&#10;            UnauthorizedException ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.UNAUTHORIZED);&#10;    }&#10;    &#10;    @ExceptionHandler(MethodArgumentNotValidException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleValidationExceptions(&#10;            MethodArgumentNotValidException ex) {&#10;        &#10;        Map&lt;String, String&gt; errors = new HashMap&lt;&gt;();&#10;        ex.getBindingResult().getAllErrors().forEach((error) -&gt; {&#10;            String fieldName = ((FieldError) error).getField();&#10;            String errorMessage = error.getDefaultMessage();&#10;            errors.put(fieldName, errorMessage);&#10;        });&#10;        &#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;Validation failed&quot;, errors.toString());&#10;        response.setData(errors);&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;    &#10;    @ExceptionHandler(MethodArgumentTypeMismatchException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleTypeMismatchException(&#10;            MethodArgumentTypeMismatchException ex, ServerWebExchange exchange) {&#10;&#10;        String message = String.format(&quot;Invalid value '%s' for parameter '%s'. Expected type: %s&quot;,&#10;                ex.getValue(), ex.getName(), ex.getRequiredType().getSimpleName());&#10;        &#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(message);&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;    &#10;    @ExceptionHandler(IllegalArgumentException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleIllegalArgumentException(&#10;            IllegalArgumentException ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;    &#10;    @ExceptionHandler(RuntimeException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleRuntimeException(&#10;            RuntimeException ex, ServerWebExchange exchange) {&#10;        ex.printStackTrace(); // Print full stack trace to console&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;An error occurred: &quot; + ex.getMessage());&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.INTERNAL_SERVER_ERROR);&#10;    }&#10;    &#10;    @ExceptionHandler(Exception.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleGlobalException(&#10;            Exception ex, ServerWebExchange exchange) {&#10;&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;Internal server error occurred&quot;);&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.INTERNAL_SERVER_ERROR);&#10;    }&#10;&#10;    @ExceptionHandler(HttpMessageNotReadableException.class)&#10;    public ResponseEntity&lt;ApiResponse&lt;Object&gt;&gt; handleHttpMessageNotReadableException(&#10;            HttpMessageNotReadableException ex, ServerWebExchange exchange) {&#10;        String expectedSchema = &quot;{\n  id: String,\n  title: String,\n  description: String,\n  type: String,\n  subject: String,\n  price: Integer,\n  originalPrice: Integer,\n  rating: Double,\n  reviews: Integer,\n  pages: Integer,\n  author: String,\n  isPremium: Integer,\n  thumbnailUrl: String,\n  status: String,\n  downloads: Integer,\n  createdAt: String (ISO 8601),\n  fileSize: String,\n  filePath: String,\n  originalName: String\n}&quot;;&#10;        String message = &quot;Malformed JSON or type mismatch: &quot; + ex.getMessage();&#10;        Map&lt;String, Object&gt; details = new HashMap&lt;&gt;();&#10;        details.put(&quot;expectedSchema&quot;, expectedSchema);&#10;        details.put(&quot;error&quot;, message);&#10;        ApiResponse&lt;Object&gt; response = ApiResponse.error(&quot;Invalid request body&quot;, details.toString());&#10;        response.setData(details);&#10;        return new ResponseEntity&lt;&gt;(response, HttpStatus.BAD_REQUEST);&#10;    }&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Dailyquizquestion.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Dailyquizquestion.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.model;&#10;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#10;import com.google.cloud.spring.data.firestore.Document;&#10;import lombok.AllArgsConstructor;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;import java.time.LocalDateTime;&#10;import java.util.*;&#10;&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class Dailyquizquestion {&#10;    @DocumentId&#10;    private String id;&#10;    private LocalDateTime date;&#10;    private String question;&#10;    private LocalDateTime date;&#10;    private Integer correctAnswer;&#10;    private String explanation;&#10;    private String subject;&#10;}" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.model;&#10;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#10;import com.google.cloud.spring.data.firestore.Document;&#10;import lombok.AllArgsConstructor;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;import java.time.LocalDateTime;&#10;import java.util.*;&#10;&#10;import static com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil.convertToLocalDateTime;&#10;&#10;@Document(collectionName = &quot;daily_quiz_questions&quot;)&#10;@Data&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class Dailyquizquestion {&#10;    @DocumentId&#10;    private String id;&#10;    private Object date;&#10;    private String question;&#10;    private List&lt;String&gt; options;&#10;    private Integer correctAnswer;&#10;    private String explanation;&#10;    private String subject;&#10;&#10;    public LocalDateTime getDateAsLocalDateTime() {&#10;        return convertToLocalDateTime(date);&#10;    }&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Exam.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Exam.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.model;&#10;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#10;import com.google.cloud.spring.data.firestore.Document;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;import java.time.LocalDateTime;&#10;import java.util.List;&#10;&#10;@Document(collectionName = &quot;exams&quot;)&#10;@Data&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class Exam {&#10;    @DocumentId&#10;    private String id;&#10;    private String title;&#10;    private String description;&#10;    private String subject;&#10;    private String category;&#10;    private Integer duration; // in minutes&#10;    private Integer totalQuestions;&#10;    private Integer totalMarks;&#10;    private Integer passingMarks;&#10;    private String difficulty;&#10;    private String status; // active, inactive, completed&#10;    private Object startTime;&#10;    private Object endTime;&#10;    private List&lt;String&gt; questionIds;&#10;    private String instructions;&#10;    private Integer isPremium;&#10;    private Integer attempts;&#10;    private Object createdAt;&#10;    private Object updatedAt;&#10;&#10;    public LocalDateTime getStartTimeAsLocalDateTime() {&#10;        return convertToLocalDateTime(startTime);&#10;        return convertToLocalDateTime(startTime);&#10;&#10;    public LocalDateTime getEndTimeAsLocalDateTime() {&#10;        return convertToLocalDateTime(endTime);&#10;    }&#10;        return convertToLocalDateTime(endTime);&#10;    public LocalDateTime getCreatedAtAsLocalDateTime() {&#10;        return convertToLocalDateTime(createdAt);&#10;    }&#10;        return convertToLocalDateTime(createdAt);&#10;    public LocalDateTime getUpdatedAtAsLocalDateTime() {&#10;        return convertToLocalDateTime(updatedAt);&#10;    }&#10;        return convertToLocalDateTime(updatedAt);&#10;    private LocalDateTime convertToLocalDateTime(Object obj) {&#10;&#10;    private LocalDateTime convertToLocalDateTime(Object obj) {&#10;        if (obj == null) return null;&#10;        if (obj instanceof LocalDateTime) return (LocalDateTime) obj;&#10;        if (obj instanceof String) {&#10;            String str = (String) obj;&#10;            try {&#10;                return java.time.LocalDateTime.parse(str);&#10;            } catch (Exception e) {&#10;                try {&#10;                    return java.time.LocalDate.parse(str).atStartOfDay();&#10;                } catch (Exception ex) {&#10;                    return null;&#10;                }&#10;            }&#10;        }&#10;        if (obj instanceof java.util.Map) {&#10;            java.util.Map map = (java.util.Map) obj;&#10;            if (map.containsKey(&quot;year&quot;) &amp;&amp; map.containsKey(&quot;monthValue&quot;) &amp;&amp; map.containsKey(&quot;dayOfMonth&quot;)) {&#10;                int year = (int) map.get(&quot;year&quot;);&#10;                int month = (int) map.get(&quot;monthValue&quot;);&#10;                int day = (int) map.get(&quot;dayOfMonth&quot;);&#10;                int hour = map.containsKey(&quot;hour&quot;) ? (int) map.get(&quot;hour&quot;) : 0;&#10;                int minute = map.containsKey(&quot;minute&quot;) ? (int) map.get(&quot;minute&quot;) : 0;&#10;                int second = map.containsKey(&quot;second&quot;) ? (int) map.get(&quot;second&quot;) : 0;&#10;                int nano = map.containsKey(&quot;nano&quot;) ? (int) map.get(&quot;nano&quot;) : 0;&#10;                return java.time.LocalDateTime.of(year, month, day, hour, minute, second, nano);&#10;            }&#10;            if (map.containsKey(&quot;_seconds&quot;)) {&#10;                long seconds = ((Number) map.get(&quot;_seconds&quot;)).longValue();&#10;                long nanos = map.containsKey(&quot;_nanoseconds&quot;) ? ((Number) map.get(&quot;_nanoseconds&quot;)).longValue() : 0L;&#10;                return java.time.LocalDateTime.ofEpochSecond(seconds, (int) nanos, java.time.ZoneOffset.UTC);&#10;            }&#10;        }&#10;        return null;&#10;    }&#10;        if (obj == null) return null;&#10;        if (obj instanceof LocalDateTime) return (LocalDateTime) obj;&#10;        if (obj instanceof String) {&#10;            String str = (String) obj;&#10;            try {&#10;                return java.time.LocalDateTime.parse(str);&#10;            } catch (Exception e) {&#10;                try {&#10;                    return java.time.LocalDate.parse(str).atStartOfDay();&#10;                } catch (Exception ex) {&#10;                    return null;&#10;                }&#10;            }&#10;        }&#10;        if (obj instanceof java.util.Map) {&#10;            java.util.Map map = (java.util.Map) obj;&#10;            if (map.containsKey(&quot;year&quot;) &amp;&amp; map.containsKey(&quot;monthValue&quot;) &amp;&amp; map.containsKey(&quot;dayOfMonth&quot;)) {&#10;                int year = (int) map.get(&quot;year&quot;);&#10;                int month = (int) map.get(&quot;monthValue&quot;);&#10;                int day = (int) map.get(&quot;dayOfMonth&quot;);&#10;                int hour = map.containsKey(&quot;hour&quot;) ? (int) map.get(&quot;hour&quot;) : 0;&#10;                int minute = map.containsKey(&quot;minute&quot;) ? (int) map.get(&quot;minute&quot;) : 0;&#10;                int second = map.containsKey(&quot;second&quot;) ? (int) map.get(&quot;second&quot;) : 0;&#10;                int nano = map.containsKey(&quot;nano&quot;) ? (int) map.get(&quot;nano&quot;) : 0;&#10;                return java.time.LocalDateTime.of(year, month, day, hour, minute, second, nano);&#10;            }&#10;            if (map.containsKey(&quot;_seconds&quot;)) {&#10;                long seconds = ((Number) map.get(&quot;_seconds&quot;)).longValue();&#10;                long nanos = map.containsKey(&quot;_nanoseconds&quot;) ? ((Number) map.get(&quot;_nanoseconds&quot;)).longValue() : 0L;&#10;                return java.time.LocalDateTime.ofEpochSecond(seconds, (int) nanos, java.time.ZoneOffset.UTC);&#10;            }&#10;        }&#10;        return null;&#10;    }&#10;}&#10;" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.model;&#10;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#10;import com.google.cloud.spring.data.firestore.Document;&#10;import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;&#10;import lombok.AllArgsConstructor;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;import java.time.LocalDateTime;&#10;import java.util.List;&#10;&#10;@Document(collectionName = &quot;exams&quot;)&#10;@Data&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class Exam {&#10;    @DocumentId&#10;    private String id;&#10;    private String title;&#10;    private String description;&#10;    private String subject;&#10;    private String category;&#10;    private Integer duration; // in minutes&#10;    private Integer totalQuestions;&#10;    private Integer totalMarks;&#10;    private Integer passingMarks;&#10;    private String difficulty;&#10;    private String status; // active, inactive, completed&#10;    private Object startTime;&#10;    private Object endTime;&#10;    private List&lt;String&gt; questionIds;&#10;    private String instructions;&#10;    private Integer isPremium;&#10;    private Integer attempts;&#10;    private Object createdAt;&#10;    private Object updatedAt;&#10;&#10;    public LocalDateTime getStartTimeAsLocalDateTime() {&#10;        return FirestoreDateTimeUtil.convertToLocalDateTime(startTime);&#10;    }&#10;&#10;    public LocalDateTime getEndTimeAsLocalDateTime() {&#10;        return FirestoreDateTimeUtil.convertToLocalDateTime(endTime);&#10;    }&#10;&#10;    public LocalDateTime getCreatedAtAsLocalDateTime() {&#10;        return FirestoreDateTimeUtil.convertToLocalDateTime(createdAt);&#10;    }&#10;&#10;    public LocalDateTime getUpdatedAtAsLocalDateTime() {&#10;        return FirestoreDateTimeUtil.convertToLocalDateTime(updatedAt);&#10;    }&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Purchase.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Purchase.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.model;&#10;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#10;import com.google.cloud.spring.data.firestore.Document;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;import java.time.LocalDateTime;&#10;&#10;@Document(collectionName = &quot;purchases&quot;)&#10;@Data&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class Purchase {&#10;    @DocumentId&#10;    private String id;&#10;    private String userId;&#10;    private String materialId;&#10;    private String planId;&#10;    private String orderId;&#10;    private String paymentId;&#10;    private Double amount;&#10;    private String currency;&#10;    private String paymentMethod; // razorpay, cashfree, upi&#10;    private String status; // pending, completed, failed, refunded&#10;    private LocalDateTime purchaseDate;&#10;    private LocalDateTime purchaseDate;&#10;    private LocalDateTime expiryDate;&#10;    private String transactionDetails;&#10;    private LocalDateTime createdAt;&#10;    private LocalDateTime updatedAt;&#10;}&#10;" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.model;&#10;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#10;import com.google.cloud.spring.data.firestore.Document;&#10;import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;&#10;import lombok.AllArgsConstructor;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;import java.time.LocalDateTime;&#10;&#10;@Document(collectionName = &quot;purchases&quot;)&#10;@Data&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class Purchase {&#10;    @DocumentId&#10;    private String id;&#10;    private String userId;&#10;    private String materialId;&#10;    private String planId;&#10;    private String orderId;&#10;    private String paymentId;&#10;    private Double amount;&#10;    private String currency;&#10;    private String paymentMethod; // razorpay, cashfree, upi&#10;    private String status; // pending, completed, failed, refunded&#10;    private Object purchaseDate;&#10;    private Object expiryDate;&#10;    private Object createdAt;&#10;    private Object updatedAt;&#10;    private Boolean accessGranted;&#10;    private Integer downloadCount;&#10;    private Integer maxDownloads;&#10;    private String transactionDetails;&#10;&#10;    public LocalDateTime getPurchaseDateAsLocalDateTime() {&#10;        return FirestoreDateTimeUtil.convertToLocalDateTime(purchaseDate);&#10;    }&#10;&#10;    public LocalDateTime getExpiryDateAsLocalDateTime() {&#10;        return FirestoreDateTimeUtil.convertToLocalDateTime(expiryDate);&#10;    }&#10;&#10;    public LocalDateTime getCreatedAtAsLocalDateTime() {&#10;        return FirestoreDateTimeUtil.convertToLocalDateTime(createdAt);&#10;    }&#10;&#10;    public LocalDateTime getUpdatedAtAsLocalDateTime() {&#10;        return FirestoreDateTimeUtil.convertToLocalDateTime(updatedAt);&#10;    }&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Recentactivity.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Recentactivity.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.model;&#10;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#10;import com.google.cloud.spring.data.firestore.Document;&#10;import lombok.AllArgsConstructor;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;import java.time.LocalDateTime;&#10;&#10;@Document(collectionName = &quot;recent_activities&quot;)&#10;@Data&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class Recentactivity {&#10;    @DocumentId&#10;    private String id;&#10;    private String user;&#10;    private String action;&#10;    private String item;&#10;    private String time;&#10;    private LocalDateTime timestamp;&#10;" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.model;&#13;&#10;&#13;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#13;&#10;import com.google.cloud.spring.data.firestore.Document;&#13;&#10;import lombok.AllArgsConstructor;&#13;&#10;import lombok.Data;&#13;&#10;import lombok.NoArgsConstructor;&#13;&#10;&#13;&#10;import java.time.LocalDateTime;&#13;&#10;&#13;&#10;@Document(collectionName = &quot;recent_activities&quot;)&#13;&#10;@Data&#13;&#10;@NoArgsConstructor&#13;&#10;@AllArgsConstructor&#13;&#10;public class Recentactivity {&#13;&#10;    @DocumentId&#13;&#10;    private String id;&#13;&#10;    private String user;&#13;&#10;    private String action;&#13;&#10;    private String item;&#13;&#10;    private String time;&#13;&#10;    private Object timestamp;&#13;&#10;&#13;&#10;    public LocalDateTime getTimestampAsLocalDateTime() {&#13;&#10;        if (timestamp == null) return null;&#13;&#10;        if (timestamp instanceof LocalDateTime) return (LocalDateTime) timestamp;&#13;&#10;        if (timestamp instanceof String) {&#13;&#10;            String str = (String) timestamp;&#13;&#10;            try {&#13;&#10;                // Try full ISO_LOCAL_DATE_TIME first&#13;&#10;                return java.time.LocalDateTime.parse(str);&#13;&#10;            } catch (Exception e) {&#13;&#10;                // Try yyyy-MM-dd&#13;&#10;                try {&#13;&#10;                    return java.time.LocalDate.parse(str).atStartOfDay();&#13;&#10;                } catch (Exception ex) {&#13;&#10;                    return null;&#13;&#10;                }&#13;&#10;            }&#13;&#10;        }&#13;&#10;        return null;&#13;&#10;    }&#13;&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Testimonial.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Testimonial.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.model;&#10;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#10;import com.google.cloud.spring.data.firestore.Document;&#10;import lombok.AllArgsConstructor;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;@Document(collectionName = &quot;testimonials&quot;)&#10;@Data&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class Testimonial {&#10;    @DocumentId&#10;    private String id;&#10;    private String name;&#10;    private String rank;&#10;    private String image;&#10;    private String quote;&#10;    private Integer featured;&#10;" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.model;&#10;&#10;import com.google.cloud.firestore.annotation.DocumentId;&#10;import com.google.cloud.spring.data.firestore.Document;&#10;import lombok.AllArgsConstructor;&#10;import lombok.Data;&#10;import lombok.NoArgsConstructor;&#10;&#10;@Document(collectionName = &quot;testimonials&quot;)&#10;@Data&#10;@NoArgsConstructor&#10;@AllArgsConstructor&#10;public class Testimonial {&#10;    @DocumentId&#10;    private String id;&#10;    private String name;&#10;    private String rank;&#10;    private String image;&#10;    private String quote;&#10;    private Object featured;&#10;&#10;    public Integer getFeaturedAsInteger() {&#10;        if (featured == null) return null;&#10;        if (featured instanceof Integer) return (Integer) featured;&#10;        if (featured instanceof Boolean) return (Boolean) featured ? 1 : 0;&#10;        if (featured instanceof String) {&#10;            try {&#10;                return Integer.parseInt((String) featured);&#10;            } catch (Exception e) {&#10;                return null;&#10;            }&#10;        }&#10;        return null;&#10;    }&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/util/FirestoreDateDeserializer.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/util/FirestoreDateDeserializer.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.util;&#10;&#10;import com.fasterxml.jackson.core.JsonParser;&#10;import com.fasterxml.jackson.core.JsonProcessingException;&#10;import com.fasterxml.jackson.databind.DeserializationContext;&#10;import com.fasterxml.jackson.databind.JsonDeserializer;&#10;import com.fasterxml.jackson.databind.JsonNode;&#10;import java.io.IOException;&#10;import java.time.LocalDateTime;&#10;import java.time.ZoneId;&#10;import java.util.Date;&#10;&#10;public class FirestoreDateDeserializer extends JsonDeserializer&lt;Date&gt; {&#10;    @Override&#10;    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {&#10;        JsonNode node = p.getCodec().readTree(p);&#10;        if (node.isObject() &amp;&amp; node.has(&quot;year&quot;) &amp;&amp; node.has(&quot;monthValue&quot;) &amp;&amp; node.has(&quot;dayOfMonth&quot;)) {&#10;            int year = node.get(&quot;year&quot;).asInt();&#10;            int month = node.get(&quot;monthValue&quot;).asInt();&#10;            int day = node.get(&quot;dayOfMonth&quot;).asInt();&#10;            int hour = node.has(&quot;hour&quot;) ? node.get(&quot;hour&quot;).asInt() : 0;&#10;            int minute = node.has(&quot;minute&quot;) ? node.get(&quot;minute&quot;).asInt() : 0;&#10;            int second = node.has(&quot;second&quot;) ? node.get(&quot;second&quot;).asInt() : 0;&#10;            int nano = node.has(&quot;nano&quot;) ? node.get(&quot;nano&quot;).asInt() : 0;&#10;            LocalDateTime ldt = LocalDateTime.of(year, month, day, hour, minute, second, nano);&#10;            return Date.from(ldt.atZone(ZoneId.systemDefault()).toInstant());&#10;        }&#10;        // fallback: try to parse as timestamp or string&#10;        if (node.isTextual()) {&#10;            try {&#10;                return Date.from(LocalDateTime.parse(node.asText()).atZone(ZoneId.systemDefault()).toInstant());&#10;            } catch (Exception e) {&#10;                // ignore&#10;            }&#10;        }&#10;" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.util;&#10;&#10;import com.fasterxml.jackson.core.JsonParser;&#10;import com.fasterxml.jackson.core.JsonProcessingException;&#10;import com.fasterxml.jackson.databind.DeserializationContext;&#10;import com.fasterxml.jackson.databind.JsonDeserializer;&#10;import com.fasterxml.jackson.databind.JsonNode;&#10;import java.io.IOException;&#10;import java.time.LocalDateTime;&#10;import java.time.ZoneId;&#10;import java.util.Date;&#10;&#10;public class FirestoreDateDeserializer extends JsonDeserializer&lt;Date&gt; {&#10;    @Override&#10;    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {&#10;        JsonNode node = p.getCodec().readTree(p);&#10;        if (node.isObject() &amp;&amp; node.has(&quot;year&quot;) &amp;&amp; node.has(&quot;monthValue&quot;) &amp;&amp; node.has(&quot;dayOfMonth&quot;)) {&#10;            int year = node.get(&quot;year&quot;).asInt();&#10;            int month = node.get(&quot;monthValue&quot;).asInt();&#10;            int day = node.get(&quot;dayOfMonth&quot;).asInt();&#10;            int hour = node.has(&quot;hour&quot;) ? node.get(&quot;hour&quot;).asInt() : 0;&#10;            int minute = node.has(&quot;minute&quot;) ? node.get(&quot;minute&quot;).asInt() : 0;&#10;            int second = node.has(&quot;second&quot;) ? node.get(&quot;second&quot;).asInt() : 0;&#10;            int nano = node.has(&quot;nano&quot;) ? node.get(&quot;nano&quot;).asInt() : 0;&#10;            LocalDateTime ldt = LocalDateTime.of(year, month, day, hour, minute, second, nano);&#10;            return Date.from(ldt.atZone(ZoneId.systemDefault()).toInstant());&#10;        }&#10;        // fallback: try to parse as timestamp or string&#10;        if (node.isTextual()) {&#10;            try {&#10;                return Date.from(LocalDateTime.parse(node.asText()).atZone(ZoneId.systemDefault()).toInstant());&#10;            } catch (Exception e) {&#10;                // ignore&#10;            }&#10;        }&#10;        // fallback: try to parse as epoch millis&#10;        if (node.isNumber()) {&#10;            return new Date(node.asLong());&#10;        }&#10;        // fallback: try to parse Firestore Timestamp map&#10;        if (node.isObject() &amp;&amp; node.has(&quot;_seconds&quot;)) {&#10;            long seconds = node.get(&quot;_seconds&quot;).asLong();&#10;            long nanos = node.has(&quot;_nanoseconds&quot;) ? node.get(&quot;_nanoseconds&quot;).asLong() : 0L;&#10;            return new Date(seconds * 1000 + nanos / 1000000);&#10;        }&#10;        // log the node for debugging&#10;        System.err.println(&quot;Could not deserialize Firestore date: &quot; + node.toString());&#10;        return null;&#10;    }&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/util/FirestoreDateTimeUtil.java">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/util/FirestoreDateTimeUtil.java" />
              <option name="originalContent" value="package com.sparkminds.brainstorm.upsc.util;&#10;&#10;import java.time.LocalDate;&#10;import java.time.LocalDateTime;&#10;import java.util.Map;&#10;&#10;public class FirestoreDateTimeUtil {&#10;    public static LocalDateTime convertToLocalDateTime(Object obj) {&#10;        if (obj == null) return null;&#10;        if (obj instanceof LocalDateTime) return (LocalDateTime) obj;&#10;        if (obj instanceof String) {&#10;            String str = (String) obj;&#10;            try {&#10;                return LocalDateTime.parse(str);&#10;            } catch (Exception e) {&#10;                try {&#10;                    return LocalDate.parse(str).atStartOfDay();&#10;                } catch (Exception ex) {&#10;                    return null;&#10;                }&#10;            }&#10;        }&#10;        if (obj instanceof Map) {&#10;            Map map = (Map) obj;&#10;            if (map.containsKey(&quot;year&quot;) &amp;&amp; map.containsKey(&quot;monthValue&quot;) &amp;&amp; map.containsKey(&quot;dayOfMonth&quot;)) {&#10;                int year = (int) map.get(&quot;year&quot;);&#10;                int month = (int) map.get(&quot;monthValue&quot;);&#10;                int day = (int) map.get(&quot;dayOfMonth&quot;);&#10;                int hour = map.containsKey(&quot;hour&quot;) ? (int) map.get(&quot;hour&quot;) : 0;&#10;                int minute = map.containsKey(&quot;minute&quot;) ? (int) map.get(&quot;minute&quot;) : 0;&#10;                int second = map.containsKey(&quot;second&quot;) ? (int) map.get(&quot;second&quot;) : 0;&#10;                int nano = map.containsKey(&quot;nano&quot;) ? (int) map.get(&quot;nano&quot;) : 0;&#10;                return LocalDateTime.of(year, month, day, hour, minute, second, nano);&#10;            }&#10;            if (map.containsKey(&quot;_seconds&quot;)) {&#10;                long seconds = ((Number) map.get(&quot;_seconds&quot;)).longValue();&#10;                long nanos = map.containsKey(&quot;_nanoseconds&quot;) ? ((Number) map.get(&quot;_nanoseconds&quot;)).longValue() : 0L;&#10;                return LocalDateTime.ofEpochSecond(seconds, (int) nanos, java.time.ZoneOffset.UTC);&#10;            }&#10;        }&#10;        return null;&#10;    }&#10;}&#10;&#10;&#10;" />
              <option name="updatedContent" value="package com.sparkminds.brainstorm.upsc.util;&#10;&#10;import java.time.LocalDate;&#10;import java.time.LocalDateTime;&#10;import java.util.Map;&#10;&#10;public class FirestoreDateTimeUtil {&#10;    public static LocalDateTime convertToLocalDateTime(Object obj) {&#10;        if (obj == null) return null;&#10;        if (obj instanceof LocalDateTime) return (LocalDateTime) obj;&#10;        if (obj instanceof String) {&#10;            String str = (String) obj;&#10;            try {&#10;                return LocalDateTime.parse(str);&#10;            } catch (Exception e) {&#10;                try {&#10;                    return LocalDate.parse(str).atStartOfDay();&#10;                } catch (Exception ex) {&#10;                    return null;&#10;                }&#10;            }&#10;        }&#10;        if (obj instanceof Map) {&#10;            Map map = (Map) obj;&#10;            if (map.containsKey(&quot;year&quot;) &amp;&amp; map.containsKey(&quot;monthValue&quot;) &amp;&amp; map.containsKey(&quot;dayOfMonth&quot;)) {&#10;                int year = ((Number) map.get(&quot;year&quot;)).intValue();&#10;                int month = ((Number) map.get(&quot;monthValue&quot;)).intValue();&#10;                int day = ((Number) map.get(&quot;dayOfMonth&quot;)).intValue();&#10;                int hour = map.containsKey(&quot;hour&quot;) ? ((Number) map.get(&quot;hour&quot;)).intValue() : 0;&#10;                int minute = map.containsKey(&quot;minute&quot;) ? ((Number) map.get(&quot;minute&quot;)).intValue() : 0;&#10;                int second = map.containsKey(&quot;second&quot;) ? ((Number) map.get(&quot;second&quot;)).intValue() : 0;&#10;                int nano = map.containsKey(&quot;nano&quot;) ? ((Number) map.get(&quot;nano&quot;)).intValue() : 0;&#10;                return LocalDateTime.of(year, month, day, hour, minute, second, nano);&#10;            }&#10;            if (map.containsKey(&quot;_seconds&quot;)) {&#10;                long seconds = ((Number) map.get(&quot;_seconds&quot;)).longValue();&#10;                long nanos = map.containsKey(&quot;_nanoseconds&quot;) ? ((Number) map.get(&quot;_nanoseconds&quot;)).longValue() : 0L;&#10;                return LocalDateTime.ofEpochSecond(seconds, (int) nanos, java.time.ZoneOffset.UTC);&#10;            }&#10;        }&#10;        return null;&#10;    }&#10;}" />
            </PendingDiffInfo>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>