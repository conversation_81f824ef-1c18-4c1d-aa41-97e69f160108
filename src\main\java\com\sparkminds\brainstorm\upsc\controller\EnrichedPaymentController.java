package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.dto.EnrichedPurchaseResponse;
import com.sparkminds.brainstorm.upsc.service.EnrichedPaymentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/payments")
public class EnrichedPaymentController {
    private static final Logger log = LoggerFactory.getLogger(EnrichedPaymentController.class);
    
    private final EnrichedPaymentService enrichedPaymentService;
    
    public EnrichedPaymentController(EnrichedPaymentService enrichedPaymentService) {
        this.enrichedPaymentService = enrichedPaymentService;
    }
    
    @GetMapping("/purchases/enriched")
    public Mono<EnrichedPurchaseResponse> getEnrichedPurchases(
            // New Pagination Parameters (Recommended - matches Razorpay API pattern)
            @RequestParam(required = false) Long from,
            @RequestParam(required = false) Long to,
            @RequestParam(required = false) Integer count,
            @RequestParam(required = false) Integer skip,
            
            // Legacy Pagination Parameters (Backward Compatibility)
            @RequestParam(required = false) Integer limit,
            @RequestParam(required = false) Integer offset,
            
            // Filter Parameters
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false, defaultValue = "true") Boolean includeRazorpayData) {
        
        log.info("Received request for enriched purchases - from: {}, to: {}, count: {}, skip: {}, limit: {}, offset: {}, userId: {}, status: {}, includeRazorpayData: {}", 
                from, to, count, skip, limit, offset, userId, status, includeRazorpayData);
        
        return enrichedPaymentService.getEnrichedPurchases(
                from, to, count, skip, limit, offset, userId, status, includeRazorpayData);
    }
}
