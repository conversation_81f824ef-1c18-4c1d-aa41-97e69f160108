package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.model.Question;
import com.sparkminds.brainstorm.upsc.service.DailyQuizService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.time.LocalDate;

@RestController
@RequestMapping("/api/daily-quizzes")
public class DailyQuizController {

    private final DailyQuizService dailyQuizService;

    public DailyQuizController(DailyQuizService dailyQuizService) {
        this.dailyQuizService = dailyQuizService;
    }

    @GetMapping("/questions/by-date")
    public Flux<Question> getDailyQuizQuestions(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return dailyQuizService.getQuestionsForDailyQuiz(date);
    }

    // Public endpoint for demo questions
    @GetMapping("/public/by-date")
    public Flux<Question> getPublicDailyQuizQuestions(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return dailyQuizService.getQuestionsForDailyQuiz(date);
    }
}