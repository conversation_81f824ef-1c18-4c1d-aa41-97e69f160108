package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class Dashboard {
    @DocumentId
    private String id;
    private String userId;
    private int totalQuizzesTaken;
    private double averageQuizScore;
    private int currentStreak;
    private int bestStreak;
    private int totalExamsTaken;
    private double averageExamScore;
    private int totalCorrectAnswers;
    private int totalIncorrectAnswers;
    private int totalUnanswered;
    private LocalDateTime lastUpdated;
}