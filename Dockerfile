# Stage 1: Build (Compile Java App)
FROM maven:3.9.6-eclipse-temurin-17 AS builder
WORKDIR /app

# Copy only pom.xml first to use Docker cache for dependencies
COPY pom.xml .
RUN mvn dependency:go-offline

# Copy the actual source code and build the JAR
COPY src ./src
RUN mvn clean package -DskipTests

# Stage 2: Run (Small, Secure Image)
FROM gcr.io/distroless/java17-debian11
WORKDIR /app

# Copy only the built JAR file from the builder stage
COPY --from=builder /app/target/springboot-backend-1.0.0.jar app.jar

# Expose port 8081 for Cloud Run
EXPOSE 8081

# Start the application
CMD ["app.jar"]