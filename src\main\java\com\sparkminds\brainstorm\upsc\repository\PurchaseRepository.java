package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.Purchase;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface PurchaseRepository extends FirestoreReactiveRepository<Purchase> {
    Flux<Purchase> findByUserId(String userId);
    Flux<Purchase> findByStatus(String status);
    Mono<Purchase> findByOrderId(String orderId);
    Flux<Purchase> findByPaymentMethod(String paymentMethod);
    Mono<Purchase> findByUserIdAndMaterialId(String userId, String materialId);
    Mono<Purchase> findByMaterialId(String materialId);
}
