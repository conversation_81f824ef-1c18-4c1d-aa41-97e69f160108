package com.sparkminds.brainstorm.upsc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
// ExamRankingResponse.java
public class ExamRankingResponse {
    private String examId;
    private String examTitle;
    private Integer totalParticipants;
    private List<RankingEntry> topPerformers;
    private RankingStatistics statistics;
    private String lastUpdated;
    // getters and setters
}

