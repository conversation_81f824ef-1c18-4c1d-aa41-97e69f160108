package com.sparkminds.brainstorm.upsc.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.Map;

public class FirestoreDateTimeUtil {
    private static final Logger logger = LoggerFactory.getLogger(FirestoreDateTimeUtil.class);
    public static LocalDateTime convertToLocalDateTime(Object obj) {
        if (obj == null) {
            logger.warn("Input object is null in convertToLocalDateTime");
            return null;
        }
        if (obj instanceof LocalDateTime) {
            return (LocalDateTime) obj;
        }
        if (obj instanceof String) {
            String str = (String) obj;
            try {
                return LocalDateTime.parse(str);
            } catch (Exception e) {
                try {
                    return LocalDate.parse(str).atStartOfDay();
                } catch (Exception ex) {
                    logger.warn("Failed to parse string as LocalDateTime or LocalDate: {}", str, ex);
                    return null;
                }
            }
        }
        if (obj instanceof Map) {
            Map map = (Map) obj;
            if (map.containsKey("year") && map.containsKey("monthValue") && map.containsKey("dayOfMonth")) {
                try {
                    int year = ((Number) map.get("year")).intValue();
                    int month = ((Number) map.get("monthValue")).intValue();
                    int day = ((Number) map.get("dayOfMonth")).intValue();
                    int hour = map.containsKey("hour") ? ((Number) map.get("hour")).intValue() : 0;
                    int minute = map.containsKey("minute") ? ((Number) map.get("minute")).intValue() : 0;
                    int second = map.containsKey("second") ? ((Number) map.get("second")).intValue() : 0;
                    int nano = map.containsKey("nano") ? ((Number) map.get("nano")).intValue() : 0;
                    return LocalDateTime.of(year, month, day, hour, minute, second, nano);
                } catch (Exception e) {
                    logger.warn("Failed to parse map as LocalDateTime: {}", map, e);
                    return null;
                }
            }
            if (map.containsKey("_seconds")) {
                try {
                    long seconds = ((Number) map.get("_seconds")).longValue();
                    long nanos = map.containsKey("_nanoseconds") ? ((Number) map.get("_nanoseconds")).longValue() : 0L;
                    return LocalDateTime.ofEpochSecond(seconds, (int) nanos, ZoneOffset.UTC);
                } catch (Exception e) {
                    logger.warn("Failed to parse Firestore timestamp: {}", map, e);
                    return null;
                }
            }
        }
        logger.warn("Unsupported object type for LocalDateTime conversion: {}", obj.getClass());
        return null;
    }

    public static Date convertToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) return null;
        return Date.from(localDateTime.atZone(ZoneId.of("UTC")).toInstant());
    }

    public static Date convertToDate(LocalDate localDate) {
        if (localDate == null) return null;
        return Date.from(localDate.atStartOfDay(ZoneId.of("UTC")).toInstant());
    }

}
