package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.Dashboard;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public interface DashboardRepository extends FirestoreReactiveRepository<Dashboard> {
    Mono<Dashboard> findByUserId(String userId);
}