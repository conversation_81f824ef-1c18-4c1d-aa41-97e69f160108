package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.repository.PurchaseRepository;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Studymaterial;
import com.sparkminds.brainstorm.upsc.repository.StudymaterialRepository;
import com.sparkminds.brainstorm.upsc.model.DownloadLimit;
import com.sparkminds.brainstorm.upsc.model.DownloadAttempt;
import com.sparkminds.brainstorm.upsc.repository.DownloadLimitRepository;
import com.sparkminds.brainstorm.upsc.repository.DownloadAttemptRepository;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import java.net.URL;
import java.time.LocalDateTime;

@Service
public class StudymaterialService {
    private static final Logger log = LoggerFactory.getLogger(StudymaterialService.class);
    private final StudymaterialRepository repository;
    private final DownloadLimitRepository downloadLimitRepository;
    private final DownloadAttemptRepository downloadAttemptRepository;
    private final PDFService pdfService;

    public StudymaterialService(StudymaterialRepository repository, DownloadLimitRepository downloadLimitRepository,
                                DownloadAttemptRepository downloadAttemptRepository, PDFService pdfService) {
        this.repository = repository;
        this.downloadLimitRepository = downloadLimitRepository;
        this.downloadAttemptRepository = downloadAttemptRepository;
        this.pdfService = pdfService;
    }

    public Flux<Studymaterial> findAll() {
        return repository.findAll();
    }

    public Mono<Studymaterial> findById(String id) {
        return repository.findById(id);
    }

    public Mono<Studymaterial> save(Studymaterial obj) {
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }

    public Mono<DownloadLimit> getDownloadLimits(String userId, String materialId) {
        return downloadLimitRepository.findByUserIdAndMaterialId(userId, materialId)
                .switchIfEmpty(Mono.just(new DownloadLimit(userId, materialId, 5, 0, LocalDateTime.now().plusMonths(1))));
    }

    public Mono<Boolean> validateDownloadToken(String token) {
        try {
            String decoded = new String(java.util.Base64.getDecoder().decode(token));
            // Add additional validation if needed (e.g., check expiry)
            return Mono.just(true);
        } catch (Exception e) {
            log.error("Invalid download token: {}", e.getMessage());
            return Mono.just(false);
        }
    }

    public Mono<Studymaterial> findByDownloadId(String downloadId) {
        return downloadAttemptRepository.findById(downloadId)
                .flatMap(attempt -> findById(attempt.getMaterialId()));
    }

    public Mono<DownloadAttempt> saveDownloadAttempt(DownloadAttempt attempt) {
        return SecurityUtils.getCurrentUserFirebaseUid()
                .switchIfEmpty(Mono.error(new IllegalStateException("User not authenticated")))
                .flatMap(firebaseUid -> {
                    if (!firebaseUid.equals(attempt.getUserId())) {
                        log.error("Attempted userId {} does not match authenticated Firebase UID {}", attempt.getUserId(), firebaseUid);
                        return Mono.error(new IllegalArgumentException("User ID mismatch"));
                    }
                    attempt.setAttemptedAt(LocalDateTime.now());
                    return downloadAttemptRepository.save(attempt)
                            .doOnSuccess(saved -> log.info("Download attempt saved for user {} and material {}", firebaseUid, attempt.getMaterialId()));
                });
    }

    public Mono<DownloadAttempt> updateDownloadProgress(String downloadId, Integer progress, String status) {
        return downloadAttemptRepository.findById(downloadId)
                .flatMap(attempt -> {
                    attempt.setProgress(progress);
                    attempt.setStatus(status);
                    if ("completed".equals(status)) {
                        attempt.setCompletedAt(LocalDateTime.now());
                    }
                    return downloadAttemptRepository.save(attempt);
                });
    }

    public Mono<DownloadLimit> completeDownload(String downloadId) {
        return downloadAttemptRepository.findById(downloadId)
                .flatMap(attempt -> {
                    attempt.setStatus("completed");
                    attempt.setCompletedAt(LocalDateTime.now());
                    return downloadAttemptRepository.save(attempt)
                            .then(updateDownloadLimits(attempt.getUserId(), attempt.getMaterialId()));
                });
    }

    private Mono<DownloadLimit> updateDownloadLimits(String userId, String materialId) {
        return downloadLimitRepository.findByUserIdAndMaterialId(userId, materialId)
                .switchIfEmpty(Mono.fromCallable(() -> {
                    DownloadLimit newLimit = new DownloadLimit();
                    newLimit.setUserId(userId);
                    newLimit.setMaterialId(materialId);
                    newLimit.setMaxDownloads(5);
                    newLimit.setUsedDownloads(0);
                    newLimit.setResetDate(LocalDateTime.now().plusMonths(1));
                    return newLimit;
                }))
                .flatMap(limit -> {
                    limit.setUsedDownloads(limit.getUsedDownloads() + 1);
                    limit.setLastDownloadAt(LocalDateTime.now());
                    return downloadLimitRepository.save(limit);
                });
    }

    public Mono<Resource> loadFileAsResource(String filePath) {
        log.info("Loading resource from filePath: {}", filePath);
        try {
            if (filePath == null || filePath.trim().isEmpty()) {
                log.error("File path is null or empty");
                return Mono.error(new RuntimeException("File path is missing for the study material"));
            }
            // Validate URL
            new URL(filePath).toURI(); // Throws if invalid
            Resource resource = new UrlResource(filePath);
            if (resource.exists() && resource.isReadable()) {
                log.info("Resource loaded successfully: {}", filePath);
                return Mono.just(resource);
            } else {
                log.error("Resource not accessible: {}", filePath);
                return Mono.error(new RuntimeException("Could not access file: " + filePath));
            }
        } catch (Exception e) {
            log.error("Error loading resource: {}", filePath, e);
            return Mono.error(new RuntimeException("Could not read file: " + filePath, e));
        }
    }

    public Mono<Studymaterial> incrementDownloadCount(String id) {
        return findById(id)
                .flatMap(material -> {
                    material.setDownloads(material.getDownloads() + 1);
                    return save(material);
                });
    }

    public Mono<Studymaterial> uploadMaterial(FilePart file, String title, String description,
                                              String subject, Integer price, String author, String tags, Integer isPremium) {
        log.info("Starting upload for material: {}", title);
        return pdfService.uploadPDF(file, "1", "study-material")
                .flatMap(uploadedPDF -> {
                    String publicUrl = uploadedPDF.getPublic_url();
                    if (publicUrl == null || publicUrl.trim().isEmpty()) {
                        log.error("PDF upload failed: public_url is null or empty");
                        return Mono.error(new RuntimeException("Failed to get public URL for uploaded PDF"));
                    }
                    log.info("PDF uploaded to SparkMate, public_url: {}", publicUrl);
                    String originalFilename = uploadedPDF.getOriginal_file_name();
                    String fileExtension = originalFilename != null && originalFilename.contains(".")
                            ? originalFilename.substring(originalFilename.lastIndexOf(".") + 1)
                            : "pdf";
                    String fileSize = formatFileSize(uploadedPDF.getFile_size());
                    Studymaterial material = new Studymaterial();
                    material.setTitle(title);
                    material.setDescription(description);
                    material.setSubject(subject);
                    material.setPrice(price);
                    material.setAuthor(author);
                    material.setFilePath(publicUrl);
                    material.setOriginalName(originalFilename != null ? originalFilename : title + ".pdf");
                    material.setFileSize(fileSize);
                    material.setType("PDF");
                    material.setStatus("published");
                    material.setDownloads(0);
                    material.setCreatedAt(java.util.Date.from(LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
                    material.setIsPremium(isPremium);
                    return repository.save(material)
                            .doOnSuccess(saved -> log.info("Study material saved with ID: {}, filePath: {}", saved.getId(), saved.getFilePath()));
                })
                .doOnError(error -> log.error("Failed to upload material: {}", error.getMessage(), error))
                .onErrorMap(e -> new RuntimeException("Failed to upload material: " + e.getMessage()));
    }

    public Mono<Boolean> canUserDownload(String contextUserId, String paramUserId, String materialId, PurchaseRepository purchaseRepository) {
        // First, check for a valid purchase using the authenticated user's ID.
        Mono<Boolean> checkContextUser = purchaseRepository.findByUserIdAndMaterialId(contextUserId, materialId)
                .map(purchase -> "completed".equals(purchase.getStatus()) && purchase.getAccessGranted())
                .doOnNext(can -> log.info("Auth check for contextUID {}: {}", contextUserId, can));

        // Create a Mono to check the parameter ID, but only if it's valid and different.
        Mono<Boolean> checkParamUser = Mono.defer(() -> {
            if (paramUserId != null && !paramUserId.isEmpty() && !paramUserId.equals(contextUserId)) {
                return purchaseRepository.findByUserIdAndMaterialId(paramUserId, materialId)
                        .map(purchase -> "completed".equals(purchase.getStatus()) && purchase.getAccessGranted())
                        .doOnNext(can -> log.info("Auth check for paramUID {}: {}", paramUserId, can));
            }
            return Mono.empty(); // If no valid paramUserId, do nothing.
        });

        // Chain the checks: try the context user first. If it's empty (no purchase found),
        // then switch to trying the param user. If both are empty, default to false.
        return checkContextUser
                .switchIfEmpty(checkParamUser)
                .defaultIfEmpty(false);
    }

    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }
}