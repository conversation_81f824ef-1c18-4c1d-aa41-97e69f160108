package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.config.FirebaseAuthenticationToken;
import com.sparkminds.brainstorm.upsc.model.UserProfile;
import com.sparkminds.brainstorm.upsc.service.UserProfileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/userProfiles")
@RequiredArgsConstructor
@Slf4j
public class UserProfileController {

    private final UserProfileService service;

    @GetMapping
    public Flux<UserProfile> getAll() {
        log.info("Fetching all user profiles");
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<ResponseEntity<UserProfile>> getById(@PathVariable String id) {
        log.info("Fetching user profile by ID: {}", id);
        return service.findById(id)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    @PostMapping
    public Mono<ResponseEntity<UserProfile>> create(@RequestBody UserProfile profile, Authentication authentication) {
        String firebaseUid;
        if (authentication instanceof FirebaseAuthenticationToken token) {
            firebaseUid = token.getUserDetails().getFirebaseUid();
        } else {
            firebaseUid = authentication.getName();
        }
        profile.setUserId(firebaseUid); // Set the userId to the Firebase UID
        log.info("Creating user profile for firebaseUid: {}", firebaseUid);
        return service.save(profile)
                .map(ResponseEntity::ok);
    }

    @PutMapping("/{id}")
    public Mono<ResponseEntity<UserProfile>> update(@PathVariable String id, @RequestBody UserProfile profile) {
        profile.setId(id);
        log.info("Updating user profile ID: {}", id);
        return service.save(profile)
                .map(ResponseEntity::ok);
    }

    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Void>> delete(@PathVariable String id) {
        log.info("Deleting user profile ID: {}", id);
        return service.delete(id)
                .thenReturn(ResponseEntity.noContent().build());
    }

    @GetMapping(params = "userId")
    public Mono<ResponseEntity<UserProfile>> getByUserId(@RequestParam String userId, Authentication authentication) {
        String firebaseUid;
        if (authentication instanceof FirebaseAuthenticationToken token) {
            firebaseUid = token.getUserDetails().getFirebaseUid();  // `getId()` returns firebase UID
        } else {
            firebaseUid = authentication.getName();
        }

        log.info("Fetching profile for authenticated firebaseUid: {}", firebaseUid);
        log.info("Fetching user profile for userId: {}", userId);
        return service.findByUserId(firebaseUid)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    /**
     * Authenticated endpoint: fetch current user's profile.
     * Firebase UID is taken from the injected authentication.
     */
    @GetMapping("/me")
    public Mono<ResponseEntity<UserProfile>> getCurrentUserProfile(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.warn("Unauthorized access to /me (no authentication)");
            return Mono.just(ResponseEntity.status(401).build());
        }

        String firebaseUid = null;

        if (authentication instanceof FirebaseAuthenticationToken token) {
            firebaseUid = token.getUserDetails().getFirebaseUid();  // `getId()` returns firebase UID
        } else {
            firebaseUid = authentication.getName();
        }

        log.info("Fetching profile for authenticated firebaseUid: {}", firebaseUid);

        String finalFirebaseUid = firebaseUid;
        return service.findByUserId(firebaseUid)
                .map(ResponseEntity::ok)
                .switchIfEmpty(Mono.fromCallable(() -> {
                    log.warn("No profile found for firebaseUid: {}", finalFirebaseUid);
                    return ResponseEntity.notFound().build();
                }));
    }

    @GetMapping("/me/minimal")
    public Mono<ResponseEntity<UserProfile>> getCurrentUserMinimalProfile(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.warn("Unauthorized access to /me/minimal (no authentication)");
            return Mono.just(ResponseEntity.status(401).build());
        }

        String firebaseUid;
        if (authentication instanceof FirebaseAuthenticationToken token) {
            firebaseUid = token.getUserDetails().getFirebaseUid();
        } else {
            firebaseUid = authentication.getName();
        }

        log.info("Fetching minimal profile for authenticated firebaseUid: {}", firebaseUid);

        String finalFirebaseUid = firebaseUid;
        return service.findByUserId(firebaseUid)
                .map(profile -> {
                    UserProfile minimalProfile = new UserProfile();
                    minimalProfile.setUserId(profile.getUserId());
                    minimalProfile.setName(profile.getName());
                    minimalProfile.setEmail(profile.getEmail());
                    minimalProfile.setPhone(profile.getPhone());
                    return minimalProfile;
                })
                .map(ResponseEntity::ok)
                .switchIfEmpty(Mono.fromCallable(() -> {
                    log.warn("No profile found for firebaseUid: {}", finalFirebaseUid);
                    return ResponseEntity.notFound().build();
                }));
    }

    @PatchMapping("/me")
    public Mono<ResponseEntity<UserProfile>> updateCurrentUserMinimalProfile(
            @RequestBody UserProfile partialProfile,
            Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.warn("Unauthorized access to /me PATCH (no authentication)");
            return Mono.just(ResponseEntity.status(401).build());
        }

        String firebaseUid;
        if (authentication instanceof FirebaseAuthenticationToken token) {
            firebaseUid = token.getUserDetails().getFirebaseUid();
        } else {
            firebaseUid = authentication.getName();
        }

        log.info("Updating minimal profile for authenticated firebaseUid: {}", firebaseUid);

        String finalFirebaseUid = firebaseUid;
        return service.findByUserId(firebaseUid)
                .flatMap(existingProfile -> {
                    // Update only name, email, and phone if provided in the request
                    if (partialProfile.getName() != null) {
                        existingProfile.setName(partialProfile.getName());
                    }
                    if (partialProfile.getEmail() != null) {
                        existingProfile.setEmail(partialProfile.getEmail());
                    }
                    if (partialProfile.getPhone() != null) {
                        existingProfile.setPhone(partialProfile.getPhone());
                    }
                    return service.save(existingProfile);
                })
                .map(ResponseEntity::ok)
                .switchIfEmpty(Mono.fromCallable(() -> {
                    log.warn("No profile found for firebaseUid: {}", finalFirebaseUid);
                    return ResponseEntity.notFound().build();
                }));
    }
}
