package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Progressdata;
import com.sparkminds.brainstorm.upsc.service.ProgressdataService;

@RestController
@RequestMapping("/api/progressData")
public class ProgressdataController {
    private final ProgressdataService service;
    
    public ProgressdataController(ProgressdataService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<Progressdata> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<Progressdata> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<Progressdata> create(@RequestBody Progressdata obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<Progressdata> update(@PathVariable String id, @RequestBody Progressdata obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}