package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.model.Exam;
import com.sparkminds.brainstorm.upsc.model.Purchase;
import com.sparkminds.brainstorm.upsc.repository.ExamRepository;
import com.sparkminds.brainstorm.upsc.repository.PurchaseRepository;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

@Service
public class PurchaseService {
    private static final Logger log = LoggerFactory.getLogger(PurchaseService.class);
    private final PurchaseRepository repository;
    private final ExamRepository examRepository;

    public PurchaseService(PurchaseRepository repository, ExamRepository examRepository) {
        this.repository = repository;
        this.examRepository = examRepository;
    }

    public Flux<Purchase> findAll() {
        return repository.findAll();
    }

    public Mono<Purchase> findById(String id) {
        return repository.findById(id);
    }

    public Mono<Purchase> save(Purchase obj) {
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }

    public Flux<Purchase> findByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public Flux<Purchase> findByStatus(String status) {
        return repository.findByStatus(status);
    }

    public Mono<Purchase> findByOrderId(String orderId) {
        return repository.findByOrderId(orderId);
    }

    public Mono<Purchase> findByMatrialId(String materialId) {
        return repository.findByMaterialId(materialId);
    }

    public Mono<Purchase> findByUserIdAndMaterialId(String materialId) {
        return SecurityUtils.getCurrentUserFirebaseUid()
                .switchIfEmpty(Mono.error(new IllegalStateException("No authenticated user found")))
                .flatMap(userId -> repository.findByUserIdAndMaterialId(userId, materialId)
                        .doOnNext(purchase -> log.info("Found purchase for user {} and material {}: {}", userId, materialId, purchase.getId()))
                        .switchIfEmpty(Mono.empty()
                                .doOnSubscribe(s -> log.debug("No purchase found for user {} and material {}", userId, materialId))));
    }

    public Mono<Purchase> createExamPurchase(String examId, Double amount, String paymentMethod, String orderId, String paymentId) {
        return SecurityUtils.getCurrentUserFirebaseUid()
                .switchIfEmpty(Mono.error(new IllegalStateException("No authenticated user found")))
                .flatMap(userId -> examRepository.findById(examId)
                        .switchIfEmpty(Mono.error(new RuntimeException("Exam not found: " + examId)))
                        .flatMap(exam -> {
                            if (exam.getIsPremium() == 0) {
                                return Mono.error(new IllegalStateException("Cannot purchase non-premium exam"));
                            }
                            if (amount == null || (amount < exam.getOfferPrice() || amount > exam.getBasePrice())) {
                                return Mono.error(new IllegalArgumentException("Amount must be between offerPrice and basePrice"));
                            }
                            Purchase purchase = new Purchase();
                            purchase.setUserId(userId);
                            purchase.setMaterialId(examId);
                            purchase.setAmount(amount);
                            purchase.setCurrency("INR");
                            purchase.setPaymentMethod(paymentMethod);
                            purchase.setOrderId(orderId);
                            purchase.setPaymentId(paymentId);
                            purchase.setStatus("completed"); // Assume payment is verified externally
                            purchase.setAccessGranted(true);
                            purchase.setDownloadCount(0);
                            purchase.setMaxDownloads(5);
                            purchase.setCreatedAt(LocalDateTime.now());
                            purchase.setUpdatedAt(LocalDateTime.now());
                            purchase.setPurchaseDate(LocalDateTime.now());
                            purchase.setExpiryDate(LocalDateTime.now().plusYears(1)); // 1-year access
                            return repository.save(purchase)
                                    .doOnSuccess(saved -> log.info("Purchase created for user {} and exam {}", userId, examId))
                                    .doOnError(error -> log.error("Failed to create purchase: {}", error.getMessage()));
                        }));
    }
}