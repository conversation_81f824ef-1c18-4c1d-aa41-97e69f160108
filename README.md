# Brainstorm UPSC - Spring Boot Backend

A comprehensive Spring Boot application for UPSC exam preparation platform with Firebase Firestore integration.

## 🚀 Features

- **User Management**: User registration, authentication, and profile management
- **Study Materials**: Comprehensive study material management with premium content
- **Exam System**: Mock exams, daily quizzes, and progress tracking
- **Notifications**: Real-time notifications for users
- **Purchase System**: Payment integration for premium materials
- **Admin Dashboard**: Statistics and content management
- **Testimonials**: User success stories and reviews
- **Pricing Plans**: Flexible subscription models
- **Demo Questions**: Sample questions for users to try before registration
- **Download Management**: Track and limit downloads of premium materials

## 🛠 Tech Stack

- **Framework**: Spring Boot 3.1.0
- **Database**: Google Cloud Firestore
- **Authentication**: Firebase Admin SDK
- **Documentation**: Swagger/OpenAPI 3
- **Build Tool**: Maven
- **Java Version**: 17
- **Reactive Programming**: Spring WebFlux with Reactor

## 📋 Prerequisites

- Java 17 or higher
- Maven 3.6+
- Google Cloud Project with Firestore enabled
- Firebase service account key
## to deploy backend below comands
maven clean install package
gcloud app deploy --project=brainstorm-upsc-466216

## ⚙️ Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd brainstorm-upsc
   ```

2. **Configure Firebase**
   - Place your Firebase service account key as `src/main/resources/your-service-account-key.json`
   - Update `application.properties` with your project details

3. **Install dependencies**
   ```bash
   mvn clean install
   ```

4. **Run the application**
   ```bash
   mvn spring-boot:run
   ```

The application will start on `http://localhost:8080`

## 📁 Project Structure

```
src/main/java/com/sparkminds/brainstorm/upsc/
├── config/          # Configuration classes
├── controller/      # REST controllers
├── dto/            # Data transfer objects
├── exception/      # Custom exceptions
├── model/          # Firestore document models
├── repository/     # Firestore repositories
└── service/        # Business logic services
```

## 🔧 Configuration

### Application Properties
```properties
# Firestore configuration
spring.cloud.gcp.project-id=brainstorm-upsc
spring.cloud.gcp.firestore.project-id=brainstorm-upsc
spring.cloud.gcp.firestore.credentials.location=file:src/main/resources/your-service-account-key.json

# Swagger documentation
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true
```

## 📚 API Documentation

Once the application is running, access the API documentation at:
- Swagger UI: `http://localhost:8080/swagger-ui.html`
- OpenAPI JSON: `http://localhost:8080/v3/api-docs`

## 🗄️ Data Models

### Core Entities
- **User**: User authentication and basic info
- **UserProfile**: Detailed user information
- **StudyMaterial**: Educational content and resources
- **Exam**: Mock exams and assessments
- **Question**: Exam questions with options
- **Purchase**: Payment and access records
- **Notification**: System notifications
- **Testimonial**: User success stories

### Analytics & Progress
- **UserDailyQuizProgress**: Daily quiz performance tracking
- **DailyQuizSubmission**: Quiz attempt records
- **ProgressData**: User progress analytics
- **UserGrowthData**: Platform growth metrics

## 🔐 Security

- Firebase Authentication integration
- CORS configuration for cross-origin requests
- Role-based access control
- Secure API endpoints

## 🚀 Deployment

### Local Development
```bash
mvn spring-boot:run
```

### Production Build
```bash
mvn clean package
java -jar target/springboot-backend-1.0.0.jar
```

## 📊 Monitoring & Logging

- Spring Boot Actuator endpoints
- Comprehensive logging configuration
- Firebase Admin SDK debugging enabled

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 API Endpoints

### Recent Materials
- `GET /api/recentMaterials` - Get all recent materials
- `GET /api/recentMaterials/{id}` - Get material by ID
- `POST /api/recentMaterials` - Create new material
- `PUT /api/recentMaterials/{id}` - Update material
- `DELETE /api/recentMaterials/{id}` - Delete material

### Testimonials
- `GET /api/testimonials` - Get all testimonials
- `GET /api/testimonials/{id}` - Get testimonial by ID
- `POST /api/testimonials` - Create new testimonial
- `PUT /api/testimonials/{id}` - Update testimonial
- `DELETE /api/testimonials/{id}` - Delete testimonial

## 🔧 Environment Variables

Create a `.env` file or set environment variables:
```
GOOGLE_CLOUD_PROJECT_ID=brainstorm-upsc
FIREBASE_CREDENTIALS_PATH=src/main/resources/your-service-account-key.json
```

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.