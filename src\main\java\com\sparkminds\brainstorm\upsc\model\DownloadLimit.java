package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Document(collectionName = "download_limit")
@Data
@NoArgsConstructor
public class DownloadLimit {

    @DocumentId
    private String id;
    private String userId;
    private String materialId;
    private Integer maxDownloads;
    private Integer usedDownloads;
    private Object lastDownloadAt;
    private Object resetDate;

    // Custom constructor for getDownloadLimits
    public DownloadLimit(String userId, String materialId, Integer maxDownloads, Integer usedDownloads, LocalDateTime resetDate) {
        this.userId = userId;
        this.materialId = materialId;
        this.maxDownloads = maxDownloads;
        this.usedDownloads = usedDownloads;
        this.resetDate = resetDate;
        this.lastDownloadAt = null; // Initialize to null since no download has occurred
    }

    public LocalDateTime getLastDownloadAtAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(lastDownloadAt);
    }

    public LocalDateTime getResetDateAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(resetDate);
    }
}