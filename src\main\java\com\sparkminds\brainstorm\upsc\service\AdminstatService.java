package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Adminstat;
import com.sparkminds.brainstorm.upsc.repository.AdminstatRepository;

@Service
public class AdminstatService {
    private final AdminstatRepository repository;
    
    public AdminstatService(AdminstatRepository repository) { 
        this.repository = repository; 
    }
    
    public Flux<Adminstat> findAll() { 
        return repository.findAll(); 
    }
    
    public Mono<Adminstat> findById(String id) { 
        return repository.findById(id); 
    }
    
    public Mono<Adminstat> save(Adminstat obj) { 
        return repository.save(obj); 
    }
    
    public Mono<Void> delete(String id) { 
        return repository.deleteById(id); 
    }
}