package com.sparkminds.brainstorm.upsc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExamResultResponse {
    private String id;
    private String examId;
    private String userId;
    private String examTitle;
    private Integer score;
    private Integer totalQuestions;
    private Integer totalMarks;
    private Integer correctAnswers;
    private Integer incorrectAnswers;
    private Integer unanswered;
    private String timeTakenMinutes;
    private Integer rank;
    private Double percentile;
    private java.util.List<com.sparkminds.brainstorm.upsc.model.ExamResult.SubjectResult> subjects;
    private String completedAt;
    private String submittedAt;
}
