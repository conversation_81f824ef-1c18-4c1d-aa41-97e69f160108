package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Document(collectionName = "recent_materials")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Recentmaterial {
    @DocumentId
    private String id;
    private String title;
    private String type;
    private String price;
}
