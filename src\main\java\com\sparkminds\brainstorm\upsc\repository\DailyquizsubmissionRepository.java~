package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.Dailyquizsubmission;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface DailyquizsubmissionRepository extends FirestoreReactiveRepository<Dailyquizsubmission> {
    Flux<Dailyquizsubmission> findByUserId(String userId);
    Flux<Dailyquizsubmission> findByDailyQuizId(String dailyQuizId);
    Mono<Dailyquizsubmission> findTopByUserIdOrderByDateDesc(String userId);

}