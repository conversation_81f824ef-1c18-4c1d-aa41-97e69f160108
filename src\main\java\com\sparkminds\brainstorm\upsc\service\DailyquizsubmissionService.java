package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.model.DailyQuiz;
import com.sparkminds.brainstorm.upsc.model.Dailyquizsubmission;
import com.sparkminds.brainstorm.upsc.model.Question;
import com.sparkminds.brainstorm.upsc.model.QuizSubmissionRequest;
import com.sparkminds.brainstorm.upsc.repository.DailyQuizRepository;
import com.sparkminds.brainstorm.upsc.repository.DailyquizsubmissionRepository;
import com.sparkminds.brainstorm.upsc.repository.QuestionRepository;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class DailyquizsubmissionService {
    private static final Logger logger = LoggerFactory.getLogger(DailyquizsubmissionService.class);
    private final DailyquizsubmissionRepository repository;
    private final DailyQuizRepository dailyQuizRepository;
    private final QuestionRepository questionRepository;
    private final UserdailyquizprogreService progressService;

    public DailyquizsubmissionService(
            DailyquizsubmissionRepository repository,
            DailyQuizRepository dailyQuizRepository,
            QuestionRepository questionRepository,
            UserdailyquizprogreService progressService) {
        this.repository = repository;
        this.dailyQuizRepository = dailyQuizRepository;
        this.questionRepository = questionRepository;
        this.progressService = progressService;
    }

    public Mono<Dailyquizsubmission> submitQuiz(String userId, QuizSubmissionRequest request) {
        if (userId == null || request.getDailyQuizId() == null) {
            return Mono.error(new IllegalArgumentException("User ID and DailyQuiz ID must not be null"));
        }

        // CHANGED: Logic to find the DailyQuiz by ID or by Date string
        Mono<DailyQuiz> dailyQuizMono = dailyQuizRepository.findById(request.getDailyQuizId())
                .switchIfEmpty(Mono.defer(() -> {
                    // Fallback: If the ID is a date string (e.g., "2023-10-27"), find the quiz by date range
                    try {
                        LocalDate localDate = LocalDate.parse(request.getDailyQuizId());
                        Date startOfDay = FirestoreDateTimeUtil.convertToDate(localDate);
                        Date startOfNextDay = FirestoreDateTimeUtil.convertToDate(localDate.plusDays(1));
                        return dailyQuizRepository.findByDateGreaterThanEqualAndDateLessThan(startOfDay, startOfNextDay).next();
                    } catch (Exception e) {
                        return Mono.empty();
                    }
                }))
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Invalid DailyQuiz ID or Date: " + request.getDailyQuizId())));

        // The rest of the submission logic remains the same...
        return dailyQuizMono.flatMap(dailyQuiz -> {
            // Check if user has already submitted this quiz and overwrite if they have
            return repository.findByUserIdAndDailyQuizId(userId, dailyQuiz.getId())
                    .defaultIfEmpty(new Dailyquizsubmission()) // If no submission exists, create a new one
                    .flatMap(existingSubmission -> {
                        // Scoring logic...
                        return questionRepository.findAllById(dailyQuiz.getQuestionIds())
                                .collectMap(Question::getId, question -> question.getOptions().indexOf(question.getCorrectAnswer()))
                                .flatMap(correctAnswersMap -> {
                                    AtomicInteger score = new AtomicInteger(0);
                                    request.getAnswers().forEach((questionId, userAnswerIndex) -> {
                                        if (userAnswerIndex != null && userAnswerIndex.equals(correctAnswersMap.get(questionId))) {
                                            score.incrementAndGet();
                                        }
                                    });

                                    // Use the existing submission object if it exists, otherwise use the new one
                                    existingSubmission.setUserId(userId);
                                    existingSubmission.setDailyQuizId(dailyQuiz.getId());
                                    existingSubmission.setDate(FirestoreDateTimeUtil.convertToDate(
                                            FirestoreDateTimeUtil.convertToLocalDateTime(dailyQuiz.getDate())
                                    ));
                                    existingSubmission.setScore(score.get());
                                    existingSubmission.setTotalQuestions(correctAnswersMap.size());
                                    existingSubmission.setAnswers(request.getAnswers());
                                    existingSubmission.setSubmittedAt(new Date()); // Always update submission time

                                    logger.info("User {} is submitting/resubmitting quiz {}. Score: {}", userId, dailyQuiz.getId(), score.get());

                                    return save(existingSubmission)
                                            .flatMap(saved -> progressService.updateProgress(existingSubmission) // Use the original object here
                                                    .thenReturn(existingSubmission));
                                });
                    });
        }).doOnError(error -> logger.error("Failed to submit quiz for user {}: {}", userId, error.getMessage()));
    }



    // ADDED: New service method to be called by the controller
    public Mono<Dailyquizsubmission> findSubmissionByUserAndQuiz(String userId, String dailyQuizId) {
        logger.info("Searching for submission for userId: {} and dailyQuizId: {}", userId, dailyQuizId);
        return repository.findByUserIdAndDailyQuizId(userId, dailyQuizId);
    }

    // ADDED: Method to get all submissions for a user (for the calendar view)
    public Flux<Dailyquizsubmission> findSubmissionsByUserId(String userId) {
        logger.info("Fetching all submissions for userId: {}", userId);
        return repository.findByUserId(userId);
    }

    public Flux<Dailyquizsubmission> findAll() {
        return repository.findAll();
    }

    public Mono<Dailyquizsubmission> findById(String id) {
        return repository.findById(id);
    }

    public Mono<Dailyquizsubmission> save(Dailyquizsubmission submission) {
        return repository.save(submission);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }
}