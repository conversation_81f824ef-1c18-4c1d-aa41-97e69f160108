package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Document(collectionName = "news_items")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Newsitem {
    @DocumentId
    private String id;
    private String title;
    private String category;
    private String date;
    private String summary;
    private String details;
    private String relevance;
    private List<String> tags;
    private String importance;
}