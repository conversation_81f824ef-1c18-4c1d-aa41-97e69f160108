package com.sparkminds.brainstorm.upsc.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil.convertToLocalDateTime;


@Document(collectionName = "daily_quizzes")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyQuiz {
    @DocumentId
    private String id;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date date;

    private List<String> questionIds;

    public LocalDateTime getDateAsLocalDateTime() {
        return convertToLocalDateTime(date);
    }

    public void setDateAsLocalDateTime(LocalDateTime dateTime) {
        this.date = FirestoreDateTimeUtil.convertToDate(dateTime);
    }
}