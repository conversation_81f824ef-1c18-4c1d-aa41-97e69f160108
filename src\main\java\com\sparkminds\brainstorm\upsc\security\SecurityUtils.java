package com.sparkminds.brainstorm.upsc.security;

import com.sparkminds.brainstorm.upsc.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.core.context.SecurityContext;
import reactor.core.publisher.Mono;

public class SecurityUtils {
    private static final Logger log = LoggerFactory.getLogger(SecurityUtils.class);

    public static Mono<String> getCurrentUserFirebaseUid() {
        return ReactiveSecurityContextHolder.getContext()
                .map(SecurityContext::getAuthentication)
                .flatMap(authentication -> {
                    if (authentication != null && authentication.isAuthenticated()) {
                        Object principal = authentication.getPrincipal();
                        if (principal instanceof String) {
                            log.info("Extracted Firebase UID from String principal: {}", principal);
                            return Mono.just((String) principal);
                        } else if (principal instanceof org.springframework.security.core.userdetails.User) {
                            String uid = ((org.springframework.security.core.userdetails.User) principal).getUsername();
                            log.info("Extracted Firebase UID from User principal: {}", uid);
                            return Mono.just(uid);
                        } else if (principal instanceof User) {
                            String uid = ((User) principal).getId(); // Use getId() to get Firebase UID
                            log.info("Extracted Firebase UID from custom User principal: {}", uid);
                            return Mono.just(uid);
                        } else {
                            log.warn("Unknown principal type: {}", principal != null ? principal.getClass().getName() : "null");
                            return Mono.empty();
                        }
                    }
                    log.warn("No authenticated user found in security context");
                    return Mono.empty();
                })
                .switchIfEmpty(Mono.defer(() -> {
                    log.error("Security context is empty or authentication is null");
                    return Mono.empty();
                }));
    }
}