package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import com.sparkminds.brainstorm.upsc.model.Recentactivity;
import com.sparkminds.brainstorm.upsc.repository.RecentactivityRepository;

@Service
public class RecentactivityService {
    private final RecentactivityRepository repository;

    public RecentactivityService(RecentactivityRepository repository) {
        this.repository = repository;
    }

    public Flux<Recentactivity> findAll() {
        return repository.findAll();
    }

    public Mono<Recentactivity> findById(String id) {
        return repository.findById(id);
    }

    public Mono<Recentactivity> save(Recentactivity obj) {
        if (obj.getTimestamp() == null) {
            obj.setTimestamp(LocalDateTime.now());
        }
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }

    public Flux<Recentactivity> findByUser(String user) {
        return repository.findByUser(user);
    }

    public Flux<Recentactivity> findByAction(String action) {
        return repository.findByAction(action);
    }
}