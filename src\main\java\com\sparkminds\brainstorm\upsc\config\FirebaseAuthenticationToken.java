package com.sparkminds.brainstorm.upsc.config;


import com.google.firebase.auth.FirebaseToken;
import com.sparkminds.brainstorm.upsc.model.User;
import lombok.Getter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

public class FirebaseAuthenticationToken extends AbstractAuthenticationToken {
    private final String idToken;
    @Getter
    private final FirebaseToken firebaseToken;
    @Getter
    private final User userDetails;

    public FirebaseAuthenticationToken(String idToken, FirebaseToken firebaseToken, Collection<? extends GrantedAuthority> authorities, User userDetails) {
        super(authorities);
        this.idToken = idToken;
        this.firebaseToken = firebaseToken;
        this.userDetails = userDetails;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return idToken;
    }

    @Override
    public Object getPrincipal() {
        return userDetails;
    }

}
