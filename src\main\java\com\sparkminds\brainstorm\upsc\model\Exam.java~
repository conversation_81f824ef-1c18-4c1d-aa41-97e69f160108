package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Document(collectionName = "exams")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Exam {
    @DocumentId
    private String id;
    private String title;
    private String description;
    private String subject;
    private String category;
    private Integer duration; // in minutes
    private Integer totalQuestions;
    private Integer totalMarks;
    private Integer passingMarks;
    private String difficulty;
    private String status; // active, inactive, completed
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date startTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date endTime;
    private List<String> questionIds;
    private String instructions;
    private Integer isPremium; // 0 or 1
    private Integer attempts;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date createdAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date updatedAt;

    // Validate isPremium
    public void setIsPremium(Integer isPremium) {
        if (isPremium != null && isPremium != 0 && isPremium != 1) {
            throw new IllegalArgumentException("isPremium must be 0 or 1");
        }
        this.isPremium = isPremium;
    }

    // Validate basePrice
    public void setBasePrice(Double basePrice) {
        if (basePrice != null && basePrice < 0) {
            throw new IllegalArgumentException("basePrice must be non-negative");
        }
        this.basePrice = basePrice;
    }

    // Validate offerPrice
    public void setOfferPrice(Double offerPrice) {
        if (offerPrice != null && offerPrice < 0) {
            throw new IllegalArgumentException("offerPrice must be non-negative");
        }
        this.offerPrice = offerPrice;
    }

    public LocalDateTime getStartTimeAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(startTime);
    }

    public LocalDateTime getEndTimeAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(endTime);
    }

    public LocalDateTime getCreatedAtAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(createdAt);
    }

    public LocalDateTime getUpdatedAtAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(updatedAt);
    }
}