package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.Userdailyquizprogre;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public interface UserdailyquizprogreRepository extends FirestoreReactiveRepository<Userdailyquizprogre> {
    // NEW: Find progress by user ID
    Mono<Userdailyquizprogre> findByUserId(String userId);
}