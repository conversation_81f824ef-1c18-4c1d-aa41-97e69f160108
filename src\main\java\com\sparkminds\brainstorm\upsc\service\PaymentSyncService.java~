package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.dto.PurchaseSyncDTO;
import com.sparkminds.brainstorm.upsc.model.Purchase;
import com.sparkminds.brainstorm.upsc.repository.PurchaseRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class PaymentSyncService {

    private static final Logger log = LoggerFactory.getLogger(PaymentSyncService.class);

    private final WebClient paymentServiceWebClient;
    private final PurchaseRepository purchaseRepository;

    public PaymentSyncService(WebClient paymentServiceWebClient, PurchaseRepository purchaseRepository) {
        this.paymentServiceWebClient = paymentServiceWebClient;
        this.purchaseRepository = purchaseRepository;
    }

    // THIS METHOD IS NOW PUBLIC and is the primary way to sync.
    public Flux<Purchase> syncPurchasesByUserId(String userId) {
        log.info("Starting purchase sync for user: {}", userId);
        Flux<PurchaseSyncDTO> paymentServicePurchases = fetchPurchasesFromPaymentService(userId);
        Mono<List<Purchase>> monolithPurchasesMono = purchaseRepository.findByUserId(userId).collectList();

        return Mono.zip(paymentServicePurchases.collectList(), monolithPurchasesMono)
                .flatMapMany(tuple -> {
                    List<PurchaseSyncDTO> sourcePurchases = tuple.getT1();
                    List<Purchase> existingPurchases = tuple.getT2();

                    Set<String> existingOrderIds = existingPurchases.stream()
                            .map(Purchase::getOrderId)
                            .collect(Collectors.toSet());

                    log.info("Found {} purchases in Payment Service and {} purchases in Monolith for user {}",
                            sourcePurchases.size(), existingPurchases.size(), userId);

                    List<PurchaseSyncDTO> newPurchasesToCreate = sourcePurchases.stream()
                            .filter(p -> p.getOrderId() != null && !existingOrderIds.contains(p.getOrderId()))
                            .collect(Collectors.toList());

                    if (newPurchasesToCreate.isEmpty()) {
                        log.info("No new purchases to sync for user {}", userId);
                        return Flux.empty();
                    }

                    log.info("Found {} new purchases to create for user {}", newPurchasesToCreate.size(), userId);

                    return Flux.fromIterable(newPurchasesToCreate)
                            .map(this::mapToPurchaseEntity)
                            .flatMap(purchaseRepository::save)
                            .doOnNext(savedPurchase -> log.info("Successfully synced and saved new purchase with orderId: {}", savedPurchase.getOrderId()));
                });
    }

    private Flux<PurchaseSyncDTO> fetchPurchasesFromPaymentService(String userId) {
        return paymentServiceWebClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/api/payments/sync-data")
                        .queryParam("userId", userId)
                        .build())
                .retrieve()
                .bodyToFlux(PurchaseSyncDTO.class)
                .doOnError(error -> log.error("Failed to fetch purchases from Payment Service for user {}: {}", userId, error.getMessage()))
                .onErrorResume(error -> Flux.empty());
    }

    private Purchase mapToPurchaseEntity(PurchaseSyncDTO dto) {
        Purchase purchase = new Purchase();
        purchase.setUserId(dto.getUserId());
        purchase.setMaterialId(dto.getMaterialId());
        purchase.setOrderId(dto.getOrderId());
        purchase.setPaymentId(dto.getPaymentId());
        purchase.setStatus(dto.getStatus());

        try {
            purchase.setPurchaseDate(LocalDateTime.parse(dto.getPurchaseDate(), DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        } catch (Exception e) {
            log.warn("Could not parse purchaseDate '{}', falling back to current time.", dto.getPurchaseDate());
            purchase.setPurchaseDate(LocalDateTime.now());
        }

        purchase.setCreatedAt(LocalDateTime.now());
        purchase.setUpdatedAt(LocalDateTime.now());
        purchase.setPaymentMethod("razorpay");
        purchase.setCurrency("INR");
        purchase.setAccessGranted(true);
        purchase.setDownloadCount(0);
        purchase.setMaxDownloads(5);
        purchase.setAmount(0.0);

        return purchase;
    }
}