package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.model.User;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import com.sparkminds.brainstorm.upsc.service.UserService;
import com.sparkminds.brainstorm.upsc.config.FirebaseAuthenticationToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {

    private final UserService service;

    @GetMapping
    public Flux<User> getAll() {
        log.info("Fetching all users");
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<ResponseEntity<User>> getById(@PathVariable String id) {
        log.info("Fetching user by ID: {}", id);
        return service.findById(id)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    @PostMapping
    public Mono<ResponseEntity<User>> create(@RequestBody User obj) {
        log.info("Creating new user with email: {}", obj.getEmail());
        return service.save(obj).map(ResponseEntity::ok);
    }

    @PutMapping("/{id}")
    public Mono<ResponseEntity<User>> update(@PathVariable String id, @RequestBody User obj) {
        obj.setId(id);
        log.info("Updating user with ID: {}", id);
        return service.save(obj).map(ResponseEntity::ok);
    }

    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Void>> delete(@PathVariable String id) {
        log.info("Deleting user with ID: {}", id);
        return service.delete(id)
                .thenReturn(ResponseEntity.noContent().build());
    }

    /**
     * Get the currently authenticated user from Firebase JWT (already set by FirebaseAuthenticationFilter).
     */
    @GetMapping(path = {"/me", "/me/"})
    public Mono<ResponseEntity<User>> getCurrentUser(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.warn("Unauthorized access to /me - no authentication context");
            return Mono.just(ResponseEntity.status(401).build());
        }

        if (authentication instanceof FirebaseAuthenticationToken token) {
            User user = token.getUserDetails();
            log.info("Authenticated user fetched from token: {}", user.getFirebaseUid());
            return Mono.just(ResponseEntity.ok(user));
        }

        log.warn("Authentication is not of type FirebaseAuthenticationToken");
        return Mono.just(ResponseEntity.status(403).build());
    }

    @GetMapping("/fetchUserid")
    public Mono<ResponseEntity<String>> fetchUserId() {
        log.info("Fetching Firebase UID for authenticated user");
        return SecurityUtils.getCurrentUserFirebaseUid()
                .map(uid -> {
                    log.info("Successfully fetched Firebase UID: {}", uid);
                    return ResponseEntity.ok(uid);
                })
                .switchIfEmpty(Mono.just(ResponseEntity.status(401).body("No authenticated user found")))
                .onErrorResume(e -> {
                    log.error("Error fetching Firebase UID: {}", e.getMessage());
                    return Mono.just(ResponseEntity.status(500).body("Internal server error"));
                });
    }
}
