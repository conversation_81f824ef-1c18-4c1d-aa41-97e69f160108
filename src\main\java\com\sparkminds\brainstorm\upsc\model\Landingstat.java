package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Document(collectionName = "landing_stats")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Landingstat {
    @DocumentId
    private String id;
    private String number;
    private String label;
}