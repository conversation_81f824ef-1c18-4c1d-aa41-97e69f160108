package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Adminstat;
import com.sparkminds.brainstorm.upsc.service.AdminstatService;

@RestController
@RequestMapping("/api/adminStats")
public class AdminstatController {
    private final AdminstatService service;
    
    public AdminstatController(AdminstatService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<Adminstat> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<Adminstat> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<Adminstat> create(@RequestBody Adminstat obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<Adminstat> update(@PathVariable String id, @RequestBody Adminstat obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}