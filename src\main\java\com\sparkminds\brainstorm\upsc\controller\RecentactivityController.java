package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Recentactivity;
import com.sparkminds.brainstorm.upsc.service.RecentactivityService;

@RestController
@RequestMapping("/api/recentActivities")
public class RecentactivityController {
    private final RecentactivityService service;

    public RecentactivityController(RecentactivityService service) {
        this.service = service;
    }

    @GetMapping
    public Flux<Recentactivity> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<Recentactivity> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @GetMapping("/user/{user}")
    public Flux<Recentactivity> getByUser(@PathVariable String user) {
        return service.findByUser(user);
    }

    @GetMapping("/action/{action}")
    public Flux<Recentactivity> getByAction(@PathVariable String action) {
        return service.findByAction(action);
    }

    @PostMapping
    public Mono<Recentactivity> create(@RequestBody Recentactivity obj) {
        return service.save(obj);
    }

    @PutMapping("/{id}")
    public Mono<Recentactivity> update(@PathVariable String id, @RequestBody Recentactivity obj) {
        obj.setId(id);
        return service.save(obj);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }
}