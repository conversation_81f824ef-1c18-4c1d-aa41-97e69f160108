package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;

@Document(collectionName = "download_attempt")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DownloadAttempt {

    @DocumentId
    private String id;

    private String userId;
    private String materialId;
    private String downloadId;
    private Object attemptedAt;
    private Object completedAt;
    private String status;
    private String ipAddress;
    private String userAgent;
    private Integer progress;
    private String fileSize;
    private Integer downloadDuration;

    public LocalDateTime getAttemptedAtAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(attemptedAt);
    }

    public LocalDateTime getCompletedAtAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(completedAt);
    }
}
