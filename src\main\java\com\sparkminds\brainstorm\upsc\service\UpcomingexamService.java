package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Upcomingexam;
import com.sparkminds.brainstorm.upsc.repository.UpcomingexamRepository;

@Service
public class UpcomingexamService {
    private final UpcomingexamRepository repository;
    
    public UpcomingexamService(UpcomingexamRepository repository) { 
        this.repository = repository; 
    }
    
    public Flux<Upcomingexam> findAll() { 
        return repository.findAll(); 
    }
    
    public Mono<Upcomingexam> findById(String id) { 
        return repository.findById(id); 
    }
    
    public Mono<Upcomingexam> save(Upcomingexam obj) { 
        return repository.save(obj); 
    }
    
    public Mono<Void> delete(String id) { 
        return repository.deleteById(id); 
    }
}