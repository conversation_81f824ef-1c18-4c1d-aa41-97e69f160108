package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.DownloadLimit;
import com.sparkminds.brainstorm.upsc.service.DownloadLimitService;

@RestController
@RequestMapping("/api/downloadLimits")
public class DownloadLimitController {
    private final DownloadLimitService service;
    
    public DownloadLimitController(DownloadLimitService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<DownloadLimit> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<DownloadLimit> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<DownloadLimit> create(@RequestBody DownloadLimit obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<DownloadLimit> update(@PathVariable String id, @RequestBody DownloadLimit obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}