package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Recentmaterial;
import com.sparkminds.brainstorm.upsc.service.RecentmaterialService;

@RestController
@RequestMapping("/api/recentMaterials")
public class RecentmaterialController {
    private final RecentmaterialService service;
    
    public RecentmaterialController(RecentmaterialService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<Recentmaterial> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<Recentmaterial> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<Recentmaterial> create(@RequestBody Recentmaterial obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<Recentmaterial> update(@PathVariable String id, @RequestBody Recentmaterial obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}