package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.spring.data.firestore.FirestoreReactiveRepository;
import com.sparkminds.brainstorm.upsc.model.Recentactivity;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

@Repository
public interface RecentactivityRepository extends FirestoreReactiveRepository<Recentactivity> {
    Flux<Recentactivity> findByUser(String user);

    Flux<Recentactivity> findByAction(String action);
}