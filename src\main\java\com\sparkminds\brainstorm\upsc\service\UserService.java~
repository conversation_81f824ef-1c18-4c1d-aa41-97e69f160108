package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import com.sparkminds.brainstorm.upsc.model.User;
import com.sparkminds.brainstorm.upsc.repository.UserRepository;

@Service
public class UserService {
    private final UserRepository repository;
    
    public UserService(UserRepository repository) { 
        this.repository = repository; 
    }
    
    public Flux<User> findAll() { 
        return repository.findAll(); 
    }
    
    public Mono<User> findById(String id) { 
        return repository.findById(id); 
    }
    
    public Mono<User> save(User obj) { 
        return repository.save(obj); 
    }
    
    public Mono<Void> delete(String id) { 
        return repository.deleteById(id); 
    }
    
    public Mono<User> findByFirebaseUid(String firebaseUid) {
        return repository.findByFirebaseUid(firebaseUid);
    }
    
    public Mono<User> createOrUpdateFromFirebase(String firebaseUid, String email, String role) {
        return repository.findByFirebaseUid(firebaseUid)
            .flatMap(existingUser -> {
                existingUser.setLastLoginAt(java.util.Date.from(LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
                return repository.save(existingUser);
            })
            .switchIfEmpty(Mono.defer(() -> {
                User newUser = new User();
                newUser.setFirebaseUid(firebaseUid);
                newUser.setEmail(email);
                newUser.setRole("student");
                newUser.setActive(true);
                newUser.setCreatedAt(java.util.Date.from(LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
                newUser.setLastLoginAt(java.util.Date.from(LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
                return repository.save(newUser);
            }));
    }
}