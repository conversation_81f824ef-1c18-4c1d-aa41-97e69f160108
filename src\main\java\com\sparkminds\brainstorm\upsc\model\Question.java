package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Document(collectionName = "questions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Question {
    @DocumentId
    private String id;
    private String question;
    private List<String> options;
    private String correctAnswer;
    private String explanation;
    private String subject;
    private String topic;
    private String category;
    private String difficulty; // easy, medium, hard
    private Integer marks;
    private Integer negativeMarks;
    private String questionType; // mcq, true_false, numerical
    private List<String> tags;
    private String imageUrl;
    private String status; // active, inactive
    private String examId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date createdAt;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date updatedAt;
}