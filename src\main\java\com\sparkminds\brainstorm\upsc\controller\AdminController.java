package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.service.AdminService;
import java.util.Map;

@RestController
@RequestMapping("/api/admin")
public class AdminController {
    private final AdminService adminService;
    
    public AdminController(AdminService adminService) {
        this.adminService = adminService;
    }

    @GetMapping("/progressData")
    public Flux<Map<String, Object>> getProgressData() {
        return adminService.getProgressData();
    }
    
    @GetMapping("/recentMaterials")
    public Flux<Map<String, Object>> getRecentMaterials() {
        return adminService.getRecentMaterials();
    }
}