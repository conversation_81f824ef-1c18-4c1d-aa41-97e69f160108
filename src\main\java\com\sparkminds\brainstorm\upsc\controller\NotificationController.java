package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Notification;
import com.sparkminds.brainstorm.upsc.service.NotificationService;

@RestController
@RequestMapping("/api/notifications")
public class NotificationController {
    private final NotificationService service;
    
    public NotificationController(NotificationService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<Notification> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<Notification> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<Notification> create(@RequestBody Notification obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<Notification> update(@PathVariable String id, @RequestBody Notification obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}