package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.sparkminds.brainstorm.upsc.model.Landingfeature;
import com.sparkminds.brainstorm.upsc.repository.LandingfeatureRepository;

@Service
public class LandingfeatureService {
    private final LandingfeatureRepository repository;

    public LandingfeatureService(LandingfeatureRepository repository) {
        this.repository = repository;
    }

    public Flux<Landingfeature> findAll() {
        return repository.findAll();
    }

    public Mono<Landingfeature> findById(String id) {
        return repository.findById(id);
    }

    public Mono<Landingfeature> save(Landingfeature obj) {
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }
}