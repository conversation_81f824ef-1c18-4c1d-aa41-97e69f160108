package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.model.Dailyquizsubmission;
import com.sparkminds.brainstorm.upsc.model.Userdailyquizprogre;
import com.sparkminds.brainstorm.upsc.repository.DailyquizsubmissionRepository;
import com.sparkminds.brainstorm.upsc.repository.UserdailyquizprogreRepository;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;

@Service
public class UserdailyquizprogreService {
    private static final Logger logger = LoggerFactory.getLogger(UserdailyquizprogreService.class);
    private final UserdailyquizprogreRepository repository;
    private final DailyquizsubmissionRepository submissionRepository;

    public UserdailyquizprogreService(UserdailyquizprogreRepository repository, DailyquizsubmissionRepository submissionRepository) {
        this.repository = repository;
        this.submissionRepository = submissionRepository;
    }

    public Mono<Userdailyquizprogre> updateProgress(Dailyquizsubmission newSubmission) {
        String userId = newSubmission.getUserId();

        Mono<Userdailyquizprogre> userProgressMono = repository.findByUserId(userId)
                .switchIfEmpty(Mono.fromCallable(() -> {
                    logger.info("No progress found for user {}. Creating new progress document.", userId);
                    Userdailyquizprogre newProgress = new Userdailyquizprogre();
                    newProgress.setUserId(userId);
                    newProgress.setTotalQuizzesTaken(0);
                    newProgress.setAverageScore(0.0);
                    newProgress.setCurrentStreak(0);
                    newProgress.setBestStreak(0);
                    newProgress.setWeeklyProgress(new ArrayList<>());
                    return newProgress;
                }));

        Mono<Dailyquizsubmission> lastSubmissionMono = submissionRepository.findTopByUserIdOrderByDateDesc(userId);

        return Mono.zip(userProgressMono, lastSubmissionMono.defaultIfEmpty(new Dailyquizsubmission()))
                .flatMap(tuple -> {
                    Userdailyquizprogre progress = tuple.getT1();
                    Dailyquizsubmission lastSubmission = tuple.getT2();

                    // Update total quizzes and average score
                    int totalQuizzes;
                    double newAverageScore;
                    // Check if this is a retake of the same day's quiz
                    if (lastSubmission.getId() != null && newSubmission.getDate() != null && lastSubmission.getDate() != null
                            && newSubmission.getDate().equals(lastSubmission.getDate())) {
                        logger.info("Retaking quiz for the same day. Recalculating average score.");
                        double totalScoreSoFar = progress.getAverageScore() * progress.getTotalQuizzesTaken();
                        totalScoreSoFar = (totalScoreSoFar - lastSubmission.getScore()) + newSubmission.getScore();
                        totalQuizzes = progress.getTotalQuizzesTaken();
                        newAverageScore = (totalQuizzes > 0) ? totalScoreSoFar / totalQuizzes : 0.0;
                    } else {
                        logger.info("New quiz submission. Updating total quizzes and average score.");
                        double totalScoreSoFar = progress.getAverageScore() * progress.getTotalQuizzesTaken();
                        totalQuizzes = progress.getTotalQuizzesTaken() + 1;
                        newAverageScore = (totalScoreSoFar + newSubmission.getScore()) / totalQuizzes;
                    }
                    progress.setTotalQuizzesTaken(totalQuizzes);
                    progress.setAverageScore(newAverageScore);

                    // Handle streak calculation
                    Date newSubmissionDateTime = newSubmission.getSubmittedAt();
                    if (newSubmissionDateTime == null) {
                        logger.warn("newSubmission.getSubmittedAtAsLocalDateTime() is null for submission ID: {}. Using quiz date as fallback.", newSubmission.getId());
                        newSubmissionDateTime = newSubmission.getDateAsLocalDateTime();
                        if (newSubmissionDateTime == null) {
                            logger.error("Both submittedAt and date fields are null for submission ID: {}. Setting streak to 1.", newSubmission.getId());
                            progress.setCurrentStreak(1); // Default to 1 if no valid date
                            if (progress.getCurrentStreak() > progress.getBestStreak()) {
                                progress.setBestStreak(progress.getCurrentStreak());
                            }
                            return repository.save(progress);
                        }
                    }
                    LocalDate newSubmissionDate = newSubmissionDateTime.toLocalDate();

                    // Check if the last submission is valid for streak calculation
                    if (lastSubmission.getId() != null && lastSubmission.getDate() != null) {
                        LocalDate lastSubmissionDate = lastSubmission.getDateAsLocalDateTime().toLocalDate();

                        // Ignore if it's the same day
                        if (!newSubmissionDate.isEqual(lastSubmissionDate)) {
                            if (newSubmissionDate.isEqual(lastSubmissionDate.plusDays(1))) {
                                progress.setCurrentStreak(progress.getCurrentStreak() + 1); // Streak continues
                            } else {
                                progress.setCurrentStreak(1); // Streak is broken, reset to 1
                            }
                        }
                    } else {
                        // This is the very first quiz for the user
                        progress.setCurrentStreak(1);
                    }

                    // Update best streak
                    if (progress.getCurrentStreak() > progress.getBestStreak()) {
                        progress.setBestStreak(progress.getCurrentStreak());
                    }

                    logger.info("Updating progress for user {}: Quizzes={}, AvgScore={}, Streak={}",
                            progress.getUserId(), progress.getTotalQuizzesTaken(),
                            String.format("%.2f", progress.getAverageScore()), progress.getCurrentStreak());

                    return repository.save(progress);
                });
    }


    public Mono<Userdailyquizprogre> findByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public Flux<Userdailyquizprogre> findAll() {
        return repository.findAll();
    }

    public Mono<Userdailyquizprogre> findById(String id) {
        return repository.findById(id);
    }

    public Mono<Userdailyquizprogre> save(Userdailyquizprogre obj) {
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }
}
