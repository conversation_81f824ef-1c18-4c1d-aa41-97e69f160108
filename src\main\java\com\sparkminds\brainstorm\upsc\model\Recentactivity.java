package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Document(collectionName = "recent_activities")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Recentactivity {
    @DocumentId
    private String id;
    private String user;
    private String action;
    private String item;
    private String time;
    private Object timestamp;

    public LocalDateTime getTimestampAsLocalDateTime() {
        if (timestamp == null) return null;
        if (timestamp instanceof LocalDateTime) return (LocalDateTime) timestamp;
        if (timestamp instanceof String) {
            String str = (String) timestamp;
            try {
                // Try full ISO_LOCAL_DATE_TIME first
                return java.time.LocalDateTime.parse(str);
            } catch (Exception e) {
                // Try yyyy-MM-dd
                try {
                    return java.time.LocalDate.parse(str).atStartOfDay();
                } catch (Exception ex) {
                    return null;
                }
            }
        }
        return null;
    }
}
