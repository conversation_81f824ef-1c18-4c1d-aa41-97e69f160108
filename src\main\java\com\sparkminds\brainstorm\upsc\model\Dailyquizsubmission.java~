package com.sparkminds.brainstorm.upsc.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;

import static com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil.convertToLocalDateTime;
import static com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil.convertToDate;

@Document(collectionName = "daily_quiz_submissions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Dailyquizsubmission {
    private static final Logger logger = LoggerFactory.getLogger(Dailyquizsubmission.class);
    @DocumentId
    private String id;
    private String userId;
    private String dailyQuizId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date date;
    private Integer score;
    private Integer totalQuestions;
    private Map<String, Integer> answers;
    private Integer timeSpent;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date submittedAt;

    public LocalDateTime getDateAsLocalDateTime() {
        return convertToLocalDateTime(date);
    }

    public void setDateAsLocalDateTime(LocalDateTime dateTime) {
        this.date = convertToDate(dateTime);
    }

    public LocalDateTime getSubmittedAtAsLocalDateTime() {
        LocalDateTime dateTime = convertToLocalDateTime(submittedAt);
        if (dateTime == null) {
            logger.warn("submittedAt is null or could not be parsed for submission ID: {}. Returning current time as fallback.", id);
            return LocalDateTime.now(ZoneId.of("UTC"));
        }
        return dateTime;
    }


    public void setSubmittedAtAsLocalDateTime(LocalDateTime dateTime) {
        this.submittedAt = convertToDate(dateTime);
    }
}