<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Application" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="com.sparkminds.brainstorm.upsc.Application" />
    <module name="springboot-backend" />
    <option name="VM_PARAMETERS" value="--add-opens java.base/java.time=ALL-UNNAMED --add-opens java.base/java.time.chrono=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.sparkminds.brainstorm.upsc.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>