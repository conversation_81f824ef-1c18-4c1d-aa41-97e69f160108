package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.model.Question;
import com.sparkminds.brainstorm.upsc.service.QuestionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.FilePart; // CHANGED: Import FilePart
import org.springframework.web.bind.annotation.*;
// REMOVED: import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

@RestController
@RequestMapping("/api/questions")
public class QuestionController {
    private final QuestionService service;
    private static final Logger log = LoggerFactory.getLogger(QuestionController.class);
    public QuestionController(QuestionService service) {
        this.service = service;
    }

    // ... (no changes to other methods)

    @GetMapping
    public Flux<Question> getAll(@RequestParam(required = false) String examId) {
        if (examId != null) {
            return service.findByExamId(examId);
        }
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<ResponseEntity<Question>> getById(@PathVariable String id) {
        return service.findById(id)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    @PostMapping
    public Mono<ResponseEntity<Question>> create(@RequestBody Question question) {
        return service.save(question)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.badRequest().build());
    }

    @PutMapping("/{id}")
    public Mono<ResponseEntity<Question>> update(@PathVariable String id, @RequestBody Question question) {
        question.setId(id);
        return service.save(question)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.badRequest().build());
    }

    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Object>> delete(@PathVariable String id) {
        return service.delete(id)
                .then(Mono.just(ResponseEntity.ok().build()))
                .onErrorResume(e -> Mono.just(ResponseEntity.badRequest().build()));
    }

    @PostMapping(value = "/upload/{examId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Mono<ResponseEntity<List<Question>>> uploadQuestions(@PathVariable String examId,
                                                                @RequestPart("file") Mono<FilePart> filePartMono) { // CHANGED: MultipartFile to FilePart
        log.info("Received upload request for examId: {}", examId);
        return filePartMono
                .doOnNext(file -> log.info("Received file: name={}, size={}, contentType={}",
                        file.filename(), file.headers().getContentLength(), file.headers().getContentType()))
                .flatMap(filePart -> service.uploadQuestionsFromCsv(examId, filePart) // CHANGED: Pass FilePart
                        .map(ResponseEntity::ok)
                        .defaultIfEmpty(ResponseEntity.badRequest().build())
                        .onErrorResume(e -> {
                            log.error("Error processing upload: {}", e.getMessage(), e);
                            return Mono.just(ResponseEntity.badRequest().body(List.of()));
                        }))
                .doOnError(e -> log.error("Error receiving file: {}", e.getMessage(), e))
                .defaultIfEmpty(ResponseEntity.badRequest().build());
    }
}