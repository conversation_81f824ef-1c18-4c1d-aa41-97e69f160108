package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Document(collectionName = "demo_questions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Demoquestion {
    @DocumentId
    private String id;
    private String question;
    private List<String> options;
    private Integer correctAnswer;
    private String explanation;
}