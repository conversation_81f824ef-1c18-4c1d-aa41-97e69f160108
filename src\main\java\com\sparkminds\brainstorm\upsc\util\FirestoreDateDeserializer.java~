package com.sparkminds.brainstorm.upsc.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;

public class FirestoreDateDeserializer extends JsonDeserializer<LocalDateTime> {
    @Override
    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        if (node.isObject() && node.has("year") && node.has("monthValue") && node.has("dayOfMonth")) {
            int year = node.get("year").asInt();
            int month = node.get("monthValue").asInt();
            int day = node.get("dayOfMonth").asInt();
            int hour = node.has("hour") ? node.get("hour").asInt() : 0;
            int minute = node.has("minute") ? node.get("minute").asInt() : 0;
            int second = node.has("second") ? node.get("second").asInt() : 0;
            int nano = node.has("nano") ? node.get("nano").asInt() : 0;
            return LocalDateTime.of(year, month, day, hour, minute, second, nano);
        }
        if (node.isTextual()) {
            return LocalDateTime.parse(node.asText());
        }
        if (node.isObject() && node.has("_seconds")) {
            long seconds = node.get("_seconds").asLong();
            int nanos = node.has("_nanoseconds") ? node.get("_nanoseconds").asInt() : 0;
            return LocalDateTime.ofEpochSecond(seconds, nanos, ZoneId.systemDefault().getRules().getOffset(LocalDateTime.now()));
        }
        throw new IOException("Could not deserialize Firestore date: " + node.toString());
    }
}