package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.DownloadAttempt;
import com.sparkminds.brainstorm.upsc.service.DownloadAttemptService;

@RestController
@RequestMapping("/api/downloadAttempts")
public class DownloadAttemptController {
    private final DownloadAttemptService service;
    
    public DownloadAttemptController(DownloadAttemptService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<DownloadAttempt> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<DownloadAttempt> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<DownloadAttempt> create(@RequestBody DownloadAttempt obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<DownloadAttempt> update(@PathVariable String id, @RequestBody DownloadAttempt obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}