package com.sparkminds.brainstorm.upsc.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnrichedPurchaseResponse {
    private String entity = "collection";
    private Long totalCount;
    private Integer returnedCount;
    private Integer offset;
    private Integer limit;
    private Boolean hasMore;
    private Integer razorpayDataFoundCount;
    private Integer razorpayDataMissingCount;
    private List<PurchaseWithDownloadAttempts> items;
}
