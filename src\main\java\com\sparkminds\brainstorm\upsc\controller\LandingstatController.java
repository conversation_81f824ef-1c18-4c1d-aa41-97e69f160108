package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Landingstat;
import com.sparkminds.brainstorm.upsc.service.LandingstatService;

@RestController
@RequestMapping("/api/landingStats")
public class LandingstatController {
    private final LandingstatService service;

    public LandingstatController(LandingstatService service) {
        this.service = service;
    }

    @GetMapping
    public Flux<Landingstat> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<Landingstat> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @PostMapping
    public Mono<Landingstat> create(@RequestBody Landingstat obj) {
        return service.save(obj);
    }

    @PutMapping("/{id}")
    public Mono<Landingstat> update(@PathVariable String id, @RequestBody Landingstat obj) {
        obj.setId(id);
        return service.save(obj);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }
}