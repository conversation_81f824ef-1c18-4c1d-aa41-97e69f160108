package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Newsitem;
import com.sparkminds.brainstorm.upsc.service.NewsitemService;

@RestController
@RequestMapping("/api/newsItems")
public class NewsitemController {
    private final NewsitemService service;

    public NewsitemController(NewsitemService service) {
        this.service = service;
    }

    @GetMapping
    public Flux<Newsitem> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<Newsitem> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @GetMapping("/category/{category}")
    public Flux<Newsitem> getByCategory(@PathVariable String category) {
        return service.findByCategory(category);
    }

    @GetMapping("/importance/{importance}")
    public Flux<Newsitem> getByImportance(@PathVariable String importance) {
        return service.findByImportance(importance);
    }

    @PostMapping
    public Mono<Newsitem> create(@RequestBody Newsitem obj) {
        return service.save(obj);
    }

    @PutMapping("/{id}")
    public Mono<Newsitem> update(@PathVariable String id, @RequestBody Newsitem obj) {
        obj.setId(id);
        return service.save(obj);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }
}