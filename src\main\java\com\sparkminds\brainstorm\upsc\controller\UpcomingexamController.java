package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Upcomingexam;
import com.sparkminds.brainstorm.upsc.service.UpcomingexamService;

@RestController
@RequestMapping("/api/upcomingExams")
public class UpcomingexamController {
    private final UpcomingexamService service;
    
    public UpcomingexamController(UpcomingexamService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<Upcomingexam> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<Upcomingexam> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<Upcomingexam> create(@RequestBody Upcomingexam obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<Upcomingexam> update(@PathVariable String id, @RequestBody Upcomingexam obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}