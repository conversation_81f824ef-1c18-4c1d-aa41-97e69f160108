package com.sparkminds.brainstorm.upsc.security;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;
import com.sparkminds.brainstorm.upsc.config.FirebaseAuthenticationToken;
import com.sparkminds.brainstorm.upsc.dto.ApiResponse;
import com.sparkminds.brainstorm.upsc.model.User;
import com.sparkminds.brainstorm.upsc.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.web.server.WebFilterExchange;
import org.springframework.security.web.server.authentication.ServerAuthenticationConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.*;
import java.util.stream.Collectors;

// Note: The FirebaseAuthenticationFilter is designed to handle Firebase authentication in a reactive Spring WebFlux application.
// It extracts the token from the request, verifies it with Firebase, retrieves user details, and sets up the authentication context.
// If any errors occur during this process, it handles them gracefully by returning appropriate error responses.
// The filter also logs important information for debugging purposes.
@Component
public class FirebaseAuthenticationFilter implements WebFilter {

    @Autowired
    private UserService userService;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String token = extractToken(exchange);
        log("Incoming Authorization token: " + token);

        if (token == null) {
            log("No token found, proceeding as anonymous.");
            return chain.filter(exchange);
        }

        return Mono.fromCallable(() -> FirebaseAuth.getInstance().verifyIdToken(token))
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(firebaseToken -> {
                    String uid = firebaseToken.getUid();
                    String email = firebaseToken.getEmail();
                    String role = (String) firebaseToken.getClaims().get("role");
                    log("Firebase UID: " + uid + ", email: " + email + ", role: " + role);

                    return userService.createOrUpdateFromFirebase(uid, email, role)
                            .flatMap(user -> {
                                if (user == null) {
                                    log("User creation failed");
                                    return handleAuthenticationError(exchange, "User creation failed", HttpStatus.INTERNAL_SERVER_ERROR);
                                }

                                UserDetail userDetails = new UserDetail(user);

                                // Build authorities
                                List<GrantedAuthority> authorities = new ArrayList<>(userDetails.getAuthorities());
                                authorities.addAll(getAuthoritiesFromToken(firebaseToken));

                                var authentication = new FirebaseAuthenticationToken(
                                        token, firebaseToken, authorities, user
                                );

                                log("Authentication set for user: " + user.getFirebaseUid());

                                return chain.filter(exchange)
                                        .contextWrite(org.springframework.security.core.context.ReactiveSecurityContextHolder.withAuthentication(authentication));
                            });
                })
                .onErrorResume(FirebaseAuthException.class, e -> {
                    log("FirebaseAuthException: " + e.getMessage());
                    return handleAuthenticationError(exchange, "Invalid or expired token", HttpStatus.UNAUTHORIZED);
                })
                .onErrorResume(Exception.class, e -> {
                    log("General authentication error: " + e.getMessage());
                    return handleAuthenticationError(exchange, "Authentication failed", HttpStatus.INTERNAL_SERVER_ERROR);
                });
    }

    private static List<GrantedAuthority> getAuthoritiesFromToken(FirebaseToken token) {
        Object rawAuthorities = token.getClaims().get("authorities");
        if (rawAuthorities instanceof List<?> rawList) {
            return rawList.stream()
                    .filter(String.class::isInstance)
                    .map(String.class::cast)
                    .map(SimpleGrantedAuthority::new)
                    .collect(Collectors.toList());
        }
        return AuthorityUtils.NO_AUTHORITIES;
    }

    private String extractToken(ServerWebExchange exchange) {
        String bearerToken = exchange.getRequest().getHeaders().getFirst("Authorization");
        return (bearerToken != null && bearerToken.startsWith("Bearer ")) ? bearerToken.substring(7) : null;
    }

    private Mono<Void> handleAuthenticationError(ServerWebExchange exchange, String message, HttpStatus status) {
        exchange.getResponse().setStatusCode(status);
        exchange.getResponse().getHeaders().setContentType(MediaType.APPLICATION_JSON);

        ApiResponse<Object> errorResponse = ApiResponse.error(message);
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());

        try {
            byte[] jsonResponse = mapper.writeValueAsBytes(errorResponse);
            return exchange.getResponse()
                    .writeWith(Mono.just(exchange.getResponse().bufferFactory().wrap(jsonResponse)));
        } catch (Exception e) {
            return Mono.error(e);
        }
    }

    private void log(String msg) {
        System.out.println("[FIREBASE FILTER] " + msg);
    }
}
