package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Pricingplan;
import com.sparkminds.brainstorm.upsc.service.PricingplanService;

@RestController
@RequestMapping("/api/pricingPlans")
public class PricingplanController {
    private final PricingplanService service;
    
    public PricingplanController(PricingplanService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<Pricingplan> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<Pricingplan> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<Pricingplan> create(@RequestBody Pricingplan obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<Pricingplan> update(@PathVariable String id, @RequestBody Pricingplan obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}