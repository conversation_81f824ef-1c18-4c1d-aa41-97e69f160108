package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.dto.DashboardResponse;
import com.sparkminds.brainstorm.upsc.repository.ExamRepository;
import com.sparkminds.brainstorm.upsc.repository.StudymaterialRepository;
import com.sparkminds.brainstorm.upsc.repository.UserRepository;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import com.sparkminds.brainstorm.upsc.service.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/dashboard")
public class DashboardController {
    private final DashboardService dashboardService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private StudymaterialRepository studymaterialRepository;

    public DashboardController(DashboardService dashboardService) {
        this.dashboardService = dashboardService;
    }

    @GetMapping("/stats")
    public Mono<DashboardStats> getDashboardStats() {
        Mono<Long> userCount = userRepository.count().defaultIfEmpty(0L);
        Mono<Long> activeExamCount = examRepository.findByStatus("active").count().defaultIfEmpty(0L);
        Mono<Long> studyMaterialCount = studymaterialRepository.count().defaultIfEmpty(0L);

        return Mono.zip(userCount, activeExamCount, studyMaterialCount)
                .map(tuple -> new DashboardStats(
                        tuple.getT1(),
                        tuple.getT2(),
                        tuple.getT3(),
                        1000.0 // Static monthly revenue as requested
                ));
    }

    @GetMapping("/progress")
    public Mono<ResponseEntity<DashboardResponse>> getUserProgress() {
        return SecurityUtils.getCurrentUserFirebaseUid()
                .flatMap(userId -> dashboardService.getOrUpdateDashboard()
                        .map(ResponseEntity::ok)
                        .switchIfEmpty(Mono.just(ResponseEntity.notFound().build())))
                .switchIfEmpty(Mono.just(ResponseEntity.status(401).body(null)))
                .onErrorResume(e -> {
                    return Mono.just(ResponseEntity.status(500).body(null));
                });
    }
}