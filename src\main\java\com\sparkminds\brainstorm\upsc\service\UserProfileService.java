package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.model.UserProfile;
import com.sparkminds.brainstorm.upsc.repository.UserProfileRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserProfileService {

    private final UserProfileRepository repository;

    public Flux<UserProfile> findAll() {
        return repository.findAll();
    }

    public Mono<UserProfile> findById(String id) {
        return repository.findById(id);
    }

    public Mono<UserProfile> save(UserProfile obj) {
        // When saving, ensure updatedAt is set, and createdAt if it's a new profile
        if (obj.getId() == null || obj.getCreatedAt() == null) {
            obj.setCreatedAt(new java.util.Date());
        }
        obj.setUpdatedAt(new java.util.Date());
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }

    /**
     * Finds a user profile by their Firebase UID (userId).
     *
     * @param userId The Firebase UID of the user.
     * @return A Mono emitting the UserProfile if found, or an empty Mono if not found.
     *         This method NO LONGER throws an error for a non-existent profile.
     */
    public Mono<UserProfile> findByUserId(String userId) {
        if (userId == null || userId.isBlank()) {
            log.warn("findByUserId called with null or blank userId.");
            return Mono.empty(); // Return empty if userId is invalid
        }
        log.info("Searching for user profile with userId: {}", userId);
        // The repository call already returns Mono.empty() if not found.
        // We simply pass this result up to the controller.
        return repository.findByUserId(userId);
    }
}