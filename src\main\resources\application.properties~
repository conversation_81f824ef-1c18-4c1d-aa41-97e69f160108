# ====================
# GCP Firestore Config
# ====================
spring.cloud.gcp.project-id=brainstorm-upsc
spring.cloud.gcp.credentials.location=classpath:your-service-account-key.json
spring.cloud.gcp.firestore.project-id=brainstorm-upsc
spring.cloud.gcp.firestore.credentials.location=classpath:your-service-account-key.json
# Logging for GCP SDK
logging.level.com.google.cloud.spring=DEBUG
logging.level.com.sparkminds.brainstorm.upsc=DEBUG

# ======================
# Optional: Emulator (for local testing only)
# ======================
# spring.cloud.gcp.firestore.emulator.enabled=true
# spring.cloud.gcp.firestore.host-port=localhost:8080
# spring.cloud.gcp.firestore.project-id=local-project

# ============
# Swagger
# ============
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true

# ============
# Server
# ============
server.port=8081

# ============
# Basic Auth (Spring Security)
# ============
spring.security.user.name=admin
spring.security.user.password=Admin@123456

# =====================
# Multipart File Upload
# =====================
# These settings should not be duplicated
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
spring.servlet.multipart.file-size-threshold=0



# ============
# Jackson JSON Date/Time Formatting
# ============
spring.jackson.date-format=yyyy-MM-dd
spring.jackson.time-zone=UTC
spring.jackson.serialization.write-dates-as-timestamps=false
