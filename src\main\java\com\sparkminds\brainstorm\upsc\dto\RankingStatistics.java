package com.sparkminds.brainstorm.upsc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RankingStatistics {
    private Double averageScore;
    private Integer averageTime;
    private Double highestScore;
    private Double lowestScore;
    private Integer passPercentage;
    private Integer totalParticipants;

}
