package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;

import com.sparkminds.brainstorm.upsc.model.Usergrowthdata;
import com.sparkminds.brainstorm.upsc.repository.UsergrowthdataRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
public class UsergrowthdataService {
    private final UsergrowthdataRepository repository;
    public UsergrowthdataService(UsergrowthdataRepository repository) { this.repository = repository; }
    public Flux<Usergrowthdata> findAll() { return repository.findAll(); }
    public Mono<Usergrowthdata> findById(String id) { return repository.findById(id); }
    public Mono<Usergrowthdata> save(Usergrowthdata obj) { return repository.save(obj); }
    public Mono<Void> delete(String id) { repository.deleteById(id);
        return null;
    }
}