runtime: java17
service: backend
instance_class: F4 # F4_1G   # good balance: ~1GB RAM, stronger CPU

automatic_scaling:
  min_instances: 1       # always have one warm instance
  max_instances: 3       # hard cap, avoids runaway billing
  target_cpu_utilization: 0.70
  target_throughput_utilization: 0.75
  max_concurrent_requests: 50   # each instance can serve ~50 active users concurrently

env_variables:
  SPRING_PROFILES_ACTIVE: "prod"
  JAVA_TOOL_OPTIONS: "--add-opens=java.base/java.time.chrono=ALL-UNNAMED"

handlers:
  - url: /.*
    script: auto
    secure: always  # Forces HTTPS
    redirect_http_response_code: 301  # Ensures proper HTTP to HTTPS redirection
    #login: optional  # Allows public access (change to "admin" for restricted endpoints)
    #expiration: "1d"  # Caches static content for 1 day
