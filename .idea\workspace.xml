<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="db824ff1-61c9-4abd-9f1b-346ce8dc00a9" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Purchase.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/model/Purchase.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="305UEJ8T6fuYlmQ0DNew1cYLAOQ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.Application.executor": "Run",
    "Application.com.sparkminds.brainstorm.upsc.Application.executor": "Run",
    "Downloaded.Files.Path.Enabled": "false",
    "Maven.springboot-backend [clean,install...].executor": "Run",
    "Maven.springboot-backend [clean].executor": "Run",
    "Maven.springboot-backend [compile].executor": "Run",
    "Maven.springboot-backend [dependency:go-offline].executor": "Run",
    "Maven.springboot-backend [install,-U].executor": "Run",
    "Maven.springboot-backend [install].executor": "Run",
    "Repository.Attach.Annotations": "false",
    "Repository.Attach.JavaDocs": "false",
    "Repository.Attach.Sources": "false",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/ChitMate/Pratham/brainstrom-payment-gateway",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.36666667",
    "settings.editor.selected.configurable": "preferences.pluginManager"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Downloads\springboot-backend-full\src\main\resources" />
      <recent name="C:\Users\<USER>\Downloads\springboot-backend-full\src\main\java\com\example\app\exception" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.sparkminds.brainstorm.upsc.config" />
      <recent name="com.sparkminds.brainstorm.upsc.dto" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn dependency:go-offline  " />
      <command value="mvn install -U" />
      <command value="mvn install" />
      <command value="mvn compile" />
      <command value="mvn clean" />
      <command value="mvn clean install package" />
    </option>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="db824ff1-61c9-4abd-9f1b-346ce8dc00a9" name="Changes" comment="" />
      <created>1752916814921</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752916814921</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
      </list>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/controller/UserController.java</url>
          <line>63</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/controller/UserController.java</url>
          <line>69</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/service/UserService.java</url>
          <line>35</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/controller/UserProfileController.java</url>
          <line>93</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/sparkminds/brainstorm/upsc/service/UserProfileService.java</url>
          <line>23</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>