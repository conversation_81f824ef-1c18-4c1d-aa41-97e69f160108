package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Document(collectionName = "progress_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Progressdata {
    @DocumentId
    private String id;
    private String userId;
    private String progressMonth;
    private Integer score;
    private Integer totalQuestions;
    private Integer correctAnswers;
    private Integer wrongAnswers;
    private Integer skippedQuestions;
    private Double accuracy;
    private Integer timeSpent; // in minutes
    private String subject;
    private String category;
    private Integer rank;
    private String status; // completed, in_progress, pending
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructor for backward compatibility
    public Progressdata(String progressMonth, Integer score) {
        this.progressMonth = progressMonth;
        this.score = score;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}