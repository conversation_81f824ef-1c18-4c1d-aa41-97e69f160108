{"users": [{"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "phone": "+91 9876543210", "role": "student", "subscription": "premium", "joinedAt": "2024-01-15", "lastActive": "2024-03-18", "status": "active", "examsCompleted": 15, "avgScore": 85}, {"id": 2, "name": "<PERSON>", "email": "<EMAIL>", "phone": "+91 9876543211", "role": "student", "subscription": "free", "joinedAt": "2024-02-10", "lastActive": "2024-03-17", "status": "active", "examsCompleted": 8, "avgScore": 78}, {"id": 3, "name": "<PERSON>", "email": "<EMAIL>", "phone": "+91 9876543212", "role": "student", "subscription": "premium", "joinedAt": "2024-01-20", "lastActive": "2024-03-15", "status": "inactive", "examsCompleted": 22, "avgScore": 92}, {"id": 4, "name": "Admin User", "email": "<EMAIL>", "phone": "+91 9876543213", "role": "admin", "subscription": "admin", "joinedAt": "2024-01-01", "lastActive": "2024-03-18", "status": "active", "examsCompleted": 0, "avgScore": 0}, {"id": 5, "name": "Sparkmind Ventures Private Limited", "email": "<EMAIL>", "phone": "+919875454545", "role": "student", "subscription": "premium", "joinedAt": "2025-07-17", "lastActive": "2025-07-17", "status": "active", "examsCompleted": 3, "avgScore": 76}, {"id": 6, "name": "<PERSON>", "email": "<EMAIL>", "phone": "+919503763700", "role": "admin", "subscription": "admin", "joinedAt": "2025-07-17", "lastActive": "2025-07-17", "status": "active", "examsCompleted": 0, "avgScore": 0}], "userProfiles": [{"userId": "1", "fullName": "<PERSON>", "email": "<EMAIL>", "mobile": "+91 9876543210", "dateOfBirth": "1995-05-15", "role": "student", "address": "123 Main Street, Downtown", "city": "Mumbai", "state": "Maharashtra", "pincode": "400001", "createdAt": "2024-01-15T00:00:00.000Z", "updatedAt": "2024-03-18T00:00:00.000Z", "avatar": "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg", "id": 1}, {"userId": "2", "fullName": "<PERSON>", "email": "<EMAIL>", "mobile": "+91 9876543211", "dateOfBirth": "1992-08-22", "role": "student", "address": "456 Park Avenue, Central", "city": "Delhi", "state": "Delhi", "pincode": "110001", "createdAt": "2024-02-10T00:00:00.000Z", "updatedAt": "2024-03-17T00:00:00.000Z", "avatar": "https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg", "id": 2}, {"userId": "3", "fullName": "<PERSON>", "email": "<EMAIL>", "mobile": "+91 9876543212", "dateOfBirth": "1990-12-10", "role": "student", "address": "789 Oak Street, Suburb", "city": "Bangalore", "state": "Karnataka", "pincode": "560001", "createdAt": "2024-01-20T00:00:00.000Z", "updatedAt": "2024-03-15T00:00:00.000Z", "avatar": "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg", "id": 3}, {"userId": "4", "fullName": "Admin User", "email": "<EMAIL>", "mobile": "+91 9876543213", "dateOfBirth": "1985-01-01", "role": "admin", "address": "Admin Office, Tech Park", "city": "Hyderabad", "state": "Telangana", "pincode": "500001", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-03-18T00:00:00.000Z", "avatar": "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg", "id": 4}, {"userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "fullName": "Sparkmind Ventures Private Limited", "email": "<EMAIL>", "mobile": "+919875454545", "dateOfBirth": "2025-07-17", "role": "student", "address": "Aldea Annexo Flno C1-1101, Sno 12/16/1 & 12/17/1 M P, <PERSON><PERSON><PERSON><PERSON>", "city": "Pune", "state": "Maharashtra", "pincode": "411021", "createdAt": "2025-07-17T14:47:23.274Z", "updatedAt": "2025-07-17T14:47:23.274Z", "avatar": "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg", "id": 5}, {"userId": "EeAgQRj4VhOIx6n2CvwTUcMM08V2", "fullName": "<PERSON>", "email": "<EMAIL>", "role": "admin", "mobile": "+919503763700", "dateOfBirth": "2024-10-23", "address": "Aldea Annexo Flno C1-1101, Sno 12/16/1 & 12/17/1 M P, <PERSON><PERSON><PERSON><PERSON>", "city": "Pune", "state": "Maharashtra", "pincode": "411021", "createdAt": "2025-07-17T14:51:30.922Z", "updatedAt": "2025-07-17T14:51:30.922Z", "avatar": "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg", "id": 6}], "studyMaterials": [{"id": "1", "title": "Current Affairs March 2024", "description": "Comprehensive current affairs compilation with 500+ questions and detailed explanations", "type": "PDF", "subject": "Current Affairs", "price": 299, "originalPrice": 399, "rating": 4.8, "reviews": 245, "pages": 150, "author": "Dr. <PERSON><PERSON>", "isPremium": true, "thumbnailUrl": "https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg", "status": "published", "downloads": 245, "createdAt": "2024-03-15", "fileSize": "15.2 MB"}, {"id": "2", "title": "Indian Polity Video Series", "description": "Complete video course covering entire Indian Polity syllabus with animations", "type": "Video", "subject": "Polity", "price": 599, "originalPrice": 799, "rating": 4.9, "reviews": 189, "duration": "24 hours", "author": "Prof. <PERSON><PERSON><PERSON>", "isPremium": true, "thumbnailUrl": "https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg", "status": "published", "downloads": 189, "createdAt": "2024-03-10", "fileSize": "2.1 GB"}, {"id": "3", "title": "Economics Mind Maps", "description": "Visual mind maps for all economics topics with memory techniques", "type": "PDF", "subject": "Economics", "price": 199, "originalPrice": 299, "rating": 4.6, "reviews": 156, "pages": 80, "author": "CA Priya Mehta....", "isPremium": false, "thumbnailUrl": "https://images.pexels.com/photos/590016/pexels-photo-590016.jpeg", "status": "published", "downloads": 156, "createdAt": "2024-03-12", "fileSize": "8.5 MB", "duration": ""}, {"id": "4", "title": "Geography Audio Lectures", "description": "Complete geography syllabus in audio format for mobile learning", "type": "Audio", "subject": "Geography", "price": 349, "originalPrice": 449, "rating": 4.7, "reviews": 203, "duration": "18 hours", "author": "Dr. <PERSON><PERSON><PERSON>", "isPremium": true, "thumbnailUrl": "https://images.pexels.com/photos/1181675/pexels-photo-1181675.jpeg", "status": "published", "downloads": 203, "createdAt": "2024-03-08", "fileSize": "1.2 GB"}, {"id": "5", "title": "History Timeline Charts", "description": "Chronological charts covering Ancient, Medieval, and Modern Indian History", "type": "PDF", "subject": "History", "price": 149, "originalPrice": 199, "rating": 4.5, "reviews": 134, "pages": 45, "author": "Prof. <PERSON><PERSON>", "isPremium": false, "thumbnailUrl": "https://images.pexels.com/photos/1164572/pexels-photo-1164572.jpeg", "status": "published", "downloads": 134, "createdAt": "2024-03-05", "fileSize": "5.8 MB"}, {"id": "6", "title": "Science & Technology Video Course", "description": "Latest developments in science and technology with regular updates", "type": "Video", "subject": "Science & Technology", "price": 449, "originalPrice": 599, "rating": 4.8, "reviews": 178, "duration": "16 hours", "author": "Dr. <PERSON><PERSON><PERSON>", "isPremium": true, "thumbnailUrl": "https://images.pexels.com/photos/2280571/pexels-photo-2280571.jpeg", "status": "published", "downloads": 178, "createdAt": "2024-03-01", "fileSize": "1.8 GB"}, {"id": 7, "title": "Indian History Notes", "description": "Comprehensive notes on Indian History", "subject": "History", "category": "notes", "tags": ["UPSC", "History", "Ancient"], "isPremium": false, "fileName": "history-notes.pdf", "originalName": "Indian History Complete Notes.pdf", "filePath": "uploads/materials/history-notes.pdf", "fileSize": 2048576, "uploadedAt": "2024-03-20T10:00:00.000Z", "downloadCount": 0, "rating": 0, "reviews": []}, {"id": 1752948467334, "title": "One Health Approach", "description": "Context: Rising outbreaks of zoonotic diseases like Nipah, Ebola, and COVID-19 have highlighted the need for a One Health Approach—a collaborative strategy linking human, animal, and environmental health.", "type": "PDF", "subject": "Current Affairs", "price": 199, "originalPrice": 299, "rating": 0, "reviews": 0, "pages": 25, "author": "Dr. Health Expert", "isPremium": true, "thumbnailUrl": "https://images.pexels.com/photos/3938023/pexels-photo-3938023.jpeg", "status": "published", "downloads": 0, "createdAt": "2025-07-19", "fileSize": 4328711, "fileName": "1752948467288-276260514-DCA 8 July 2025.pdf", "originalName": "DCA 8 July 2025.pdf", "filePath": "uploads\\materials\\1752948467288-276260514-DCA 8 July 2025.pdf", "uploadedAt": "2025-07-19T18:07:47.334Z", "downloadCount": 6, "category": "notes", "tags": ["upsc", "current-affairs", "health"]}, {"id": 1752948679788, "title": "Current Affairs Daily Update", "description": "Latest current affairs compilation with important updates for competitive exams", "type": "PDF", "subject": "Current Affairs", "price": 149, "originalPrice": 199, "rating": 0, "reviews": 0, "pages": 30, "author": "Current Affairs Team", "isPremium": false, "thumbnailUrl": "https://images.pexels.com/photos/518543/pexels-photo-518543.jpeg", "status": "published", "downloads": 0, "createdAt": "2025-07-19", "fileSize": 4328711, "fileName": "1752948679765-257816133-DCA 8 July 2025.pdf", "originalName": "DCA 8 July 2025.pdf", "filePath": "uploads\\materials\\1752948679765-257816133-DCA 8 July 2025.pdf", "uploadedAt": "2025-07-19T18:11:19.788Z", "downloadCount": 0, "category": "notes", "tags": ["current-affairs", "daily-update"]}, {"id": 1752948886737, "title": "UPSC Practice Test Results Analysis", "description": "Comprehensive analysis of practice test performance with improvement strategies", "type": "PDF", "subject": "Economics", "price": 99, "originalPrice": 149, "rating": 0, "reviews": 0, "pages": 15, "author": "Test Analysis Team", "isPremium": false, "thumbnailUrl": "https://images.pexels.com/photos/590016/pexels-photo-590016.jpeg", "status": "published", "downloads": 0, "createdAt": "2025-07-19", "fileSize": 217690, "fileName": "1752948886730-475479397-YCSVP_Practice_Test_April_2025_Results_2025-07-05.pdf", "originalName": "YCSVP_Practice_Test_April_2025_Results_2025-07-05.pdf", "filePath": "uploads\\materials\\1752948886730-475479397-YCSVP_Practice_Test_April_2025_Results_2025-07-05.pdf", "uploadedAt": "2025-07-19T18:14:46.737Z", "downloadCount": 0, "category": "test-analysis", "tags": ["practice-test", "analysis", "economics"]}], "exams": [{"id": "1", "title": "UPSC Prelims Mock Test 16", "description": "Comprehensive test covering all subjects with detailed analysis", "type": "<PERSON><PERSON>", "duration": 120, "questions": 100, "totalMarks": 200, "marksPerQuestion": 2, "negativeMarking": 0.66, "participants": 1250, "difficulty": "Hard", "status": "upcoming", "startDate": "2024-03-20", "startTime": "10:00 AM", "createdAt": "2024-03-15", "scheduledAt": "2024-03-20 10:00", "creator": "Admin", "score": null}, {"id": "2", "title": "Current Affairs Weekly Test", "description": "Latest current affairs covering March 2024", "type": "Current Affairs", "duration": 60, "questions": 50, "totalMarks": 100, "marksPerQuestion": 2, "negativeMarking": 0.66, "participants": 890, "difficulty": "Medium", "status": "live", "startDate": "2024-03-22", "startTime": "2:00 PM", "createdAt": "2024-03-18", "scheduledAt": "2024-03-22 14:00", "creator": "Admin", "score": null}, {"id": "3", "title": "Indian History Practice Test", "description": "Ancient, Medieval and Modern Indian History comprehensive test", "type": "Subject Test", "duration": 90, "questions": 75, "totalMarks": 150, "marksPerQuestion": 2, "negativeMarking": 0.66, "participants": 650, "difficulty": "Medium", "status": "completed", "startDate": "2024-03-25", "startTime": "11:00 AM", "createdAt": "2024-03-16", "scheduledAt": "2024-03-25 11:00", "creator": "Admin", "score": 85}, {"id": "4", "title": "Geography Mock Test", "description": "Physical and Human Geography with map-based questions", "type": "Subject Test", "duration": 75, "questions": 60, "totalMarks": 120, "marksPerQuestion": 2, "negativeMarking": 0.66, "participants": 420, "difficulty": "Easy", "status": "upcoming", "startDate": "2024-03-28", "startTime": "3:00 PM", "createdAt": "2024-03-17", "scheduledAt": "2024-03-28 15:00", "creator": "Admin", "score": null}], "questions": [{"id": "1", "examId": "1", "questionNumber": 1, "question": "Which of the following is considered the most important source of information about the Mauryan administration?", "options": ["<PERSON><PERSON><PERSON><PERSON>", "Indica", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "correctAnswer": 0, "explanation": "<PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON> is the most comprehensive source of information about Mauryan administration, detailing various aspects of governance, economy, and military organization.", "subject": "History", "difficulty": "Medium", "marks": 2, "negativeMarks": 0.66}, {"id": "2", "examId": "1", "questionNumber": 2, "question": "The concept of 'Mandate of Heaven' is associated with which civilization?", "options": ["Egyptian", "Mesopotamian", "Chinese", "Indian"], "correctAnswer": 2, "explanation": "The Mandate of Heaven is a Chinese political and religious doctrine used to justify the rule of the Emperor of China. It was first used by the Zhou Dynasty.", "subject": "History", "difficulty": "Easy", "marks": 2, "negativeMarks": 0.66}, {"id": "3", "examId": "1", "questionNumber": 3, "question": "Which Article of the Indian Constitution deals with the Right to Education?", "options": ["Article 19", "Article 21A", "Article 25", "Article 32"], "correctAnswer": 1, "explanation": "Article 21A was inserted by the 86th Constitutional Amendment Act, 2002, making education a fundamental right for children aged 6-14 years.", "subject": "Polity", "difficulty": "Medium", "marks": 2, "negativeMarks": 0.66}, {"id": "4", "examId": "1", "questionNumber": 4, "question": "The Tropic of Cancer does NOT pass through which of the following states?", "options": ["Rajasthan", "Chhattisgarh", "Odisha", "<PERSON>ura"], "correctAnswer": 2, "explanation": "The Tropic of Cancer passes through Gujarat, Rajasthan, Madhya Pradesh, Chhattisgarh, Jharkhand, West Bengal, Tripura, and Mizoram. It does not pass through Odisha.", "subject": "Geography", "difficulty": "Medium", "marks": 2, "negativeMarks": 0.66}, {"id": "5", "examId": "1", "questionNumber": 5, "question": "Who was the first Governor-General of independent India?", "options": ["Lord <PERSON>", "<PERSON><PERSON>", "<PERSON>", "Lord <PERSON><PERSON><PERSON>"], "correctAnswer": 0, "explanation": "Lord <PERSON> was the first Governor-General of independent India from August 15, 1947, to June 21, 1948. He was succeeded by <PERSON><PERSON>.", "subject": "History", "difficulty": "Easy", "marks": 2, "negativeMarks": 0.66}, {"id": "6", "examId": "2", "questionNumber": 1, "question": "Which country recently became the 32nd member of NATO?", "options": ["Finland", "Sweden", "Ukraine", "Georgia"], "correctAnswer": 0, "explanation": "Finland became NATO's 32nd member in April 2023, marking a significant shift in European security architecture following Russia's invasion of Ukraine.", "subject": "Current Affairs", "difficulty": "Easy", "marks": 2, "negativeMarks": 0.66}, {"id": "7", "examId": "2", "questionNumber": 2, "question": "The Reserve Bank of India's Monetary Policy Committee (MPC) consists of how many members?", "options": ["4", "6", "8", "10"], "correctAnswer": 1, "explanation": "The MPC consists of 6 members - 3 from RBI (including Governor as Chairman) and 3 external members appointed by the Government of India.", "subject": "Economy", "difficulty": "Medium", "marks": 2, "negativeMarks": 0.66}], "dailyQuizQuestions": [{"id": "dq1", "date": "2024-03-20", "question": "Which article of the Indian Constitution deals with the Right to Education?", "options": ["Article 21", "Article 21A", "Article 19", "Article 14"], "correctAnswer": 1, "explanation": "Article 21A was inserted by the 86th Constitutional Amendment Act, 2002, making education a fundamental right for children aged 6-14 years.", "subject": "Polity"}, {"id": "dq2", "date": "2024-03-20", "question": "The Tropic of Cancer does NOT pass through which of the following states?", "options": ["Rajasthan", "Chhattisgarh", "Odisha", "<PERSON>ura"], "correctAnswer": 2, "explanation": "The Tropic of Cancer passes through Gujarat, Rajasthan, Madhya Pradesh, Chhattisgarh, Jharkhand, West Bengal, Tripura, and Mizoram. It does not pass through Odisha.", "subject": "Geography"}, {"id": "dq3", "date": "2024-03-21", "question": "Who was the first Governor-General of independent India?", "options": ["Lord <PERSON>", "<PERSON><PERSON>", "<PERSON>", "Lord <PERSON><PERSON><PERSON>"], "correctAnswer": 0, "explanation": "Lord <PERSON> was the first Governor-General of independent India from August 15, 1947, to June 21, 1948.", "subject": "History"}, {"id": "dq4", "date": "2024-03-21", "question": "The concept of 'Creamy Layer' is associated with which of the following?", "options": ["SC reservation", "ST reservation", "OBC reservation", "EWS reservation"], "correctAnswer": 2, "explanation": "The creamy layer concept applies to OBC (Other Backward Classes) reservation, excluding the affluent sections within OBCs from reservation benefits.", "subject": "Polity"}], "testimonials": [{"id": "1", "name": "<PERSON><PERSON>", "rank": "AIR 23, UPSC CSE 2023", "image": "https://images.pexels.com/photos/3769021/pexels-photo-3769021.jpeg", "quote": "Brainstorm's mock tests were exactly like the real UPSC exam. The detailed analysis helped me identify my weak areas and improve systematically.", "featured": true}, {"id": "2", "name": "<PERSON><PERSON>", "rank": "AIR 45, UPSC CSE 2023", "image": "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg", "quote": "The current affairs materials are updated daily and cover everything needed for both Prelims and Mains. Highly recommended for serious aspirants.", "featured": true}, {"id": "3", "name": "<PERSON>", "rank": "AIR 67, UPSC CSE 2023", "image": "https://images.pexels.com/photos/3763188/pexels-photo-3763188.jpeg", "quote": "The personalized study plan and progress tracking kept me motivated throughout my preparation journey. Thank you Brainstorm!", "featured": true}], "pricingPlans": [{"id": "1", "name": "Basic", "price": "₹999", "period": "/month", "features": ["Weekly mock tests", "Basic study materials", "Performance analytics", "Email support"], "popular": false, "active": true}, {"id": "2", "name": "Premium", "price": "₹2,999", "period": "/year", "originalPrice": "₹4,999", "features": ["All Basic features", "Premium study materials", "Video lectures", "Expert mentorship", "Priority support", "Mobile app access"], "popular": true, "active": true}, {"id": "3", "name": "Elite", "price": "₹4,999", "period": "/year", "originalPrice": "₹7,999", "features": ["All Premium features", "1-on-1 mentoring sessions", "Interview preparation", "Personality development", "Exclusive masterclasses", "Lifetime access"], "popular": false, "active": true}], "purchases": [{"id": "1", "userId": 2, "materialId": 1, "paymentId": "pay_sample123", "orderId": "order_sample123", "amount": 299, "currency": "INR", "status": "completed", "paymentMethod": "card", "purchaseDate": "2024-03-15T10:30:00Z", "accessGranted": true, "downloadCount": 3, "lastAccessedAt": "2024-03-16T08:45:00Z"}, {"id": "1750076646523", "userId": 1, "materialId": "1", "paymentId": "order_1_1_1750076602023", "orderId": "order_1_1_1750076602023", "amount": 299, "currency": "INR", "paymentMethod": "cashfree", "status": "completed", "purchaseDate": "2025-06-16T12:24:06.523Z", "accessGranted": true, "downloadCount": 0, "lastAccessedAt": "2025-06-16T12:24:06.524Z"}, {"id": "1750076719630", "userId": 1, "materialId": "2", "paymentId": "order_2_1_1750076683818", "orderId": "order_2_1_1750076683818", "amount": 599, "currency": "INR", "paymentMethod": "cashfree", "status": "completed", "purchaseDate": "2025-06-16T12:25:19.630Z", "accessGranted": true, "downloadCount": 0, "lastAccessedAt": "2025-06-16T12:25:19.630Z"}, {"id": "1750076851025", "userId": 1, "materialId": "5", "paymentId": "order_5_1_1750076742493", "orderId": "order_5_1_1750076742493", "amount": 149, "currency": "INR", "paymentMethod": "cashfree", "status": "completed", "purchaseDate": "2025-06-16T12:27:31.025Z", "accessGranted": true, "downloadCount": 0, "lastAccessedAt": "2025-06-16T12:27:31.025Z"}, {"id": 1752042805572, "userId": 1, "materialId": "2", "paymentId": "pay_Qqs52gubyZUNrA", "orderId": "order_Qqs4MVhZ83rrjZ", "amount": 599, "currency": "INR", "paymentMethod": "upi", "status": "completed", "purchaseDate": "2025-07-09T06:33:25.572Z", "accessGranted": true, "downloadCount": 0, "lastAccessedAt": "2025-07-09T06:33:25.573Z"}, {"id": 1752042900260, "userId": 1, "materialId": "1", "paymentId": "order_1_1_1752042857311", "orderId": "order_1_1_1752042857311", "amount": 299, "currency": "INR", "paymentMethod": "cashfree", "status": "completed", "purchaseDate": "2025-07-09T06:35:00.260Z", "accessGranted": true, "downloadCount": 0, "lastAccessedAt": "2025-07-09T06:35:00.260Z"}], "notifications": [{"id": "1", "title": "New Weekly Exam Available", "message": "UPSC Prelims Mock Test 15 is now available. Don't miss the deadline!", "type": "info", "timestamp": "2024-03-19T10:00:00.000Z", "read": false, "audience": "all", "status": "sent", "sentAt": "2024-03-18 10:00", "recipients": 2847, "openRate": 78}, {"id": "2", "title": "Study Material Updated", "message": "Current Affairs package for March 2024 has been updated with latest content.", "type": "success", "timestamp": "2024-03-18T14:30:00.000Z", "read": false, "audience": "premium", "status": "sent", "sentAt": "2024-03-17 14:30", "recipients": 1250, "openRate": 85}, {"id": "3", "title": "System Maintenance Notice", "message": "Scheduled maintenance on March 20th from 2:00 AM to 4:00 AM IST.", "type": "warning", "timestamp": "2024-03-17T09:00:00.000Z", "read": true, "audience": "all", "status": "scheduled", "sentAt": "2024-03-20 02:00", "recipients": 2847, "openRate": 0}], "adminStats": [{"id": "1", "label": "Total Users", "value": "2,847", "change": "+12%", "icon": "Users", "color": "bg-blue-500"}, {"id": "2", "label": "Active Exams", "value": "24", "change": "+3", "icon": "FileText", "color": "bg-green-500"}, {"id": "3", "label": "Study Materials", "value": "156", "change": "+8", "icon": "BookOpen", "color": "bg-purple-500"}, {"id": "4", "label": "Monthly Revenue", "value": "₹4.2L", "change": "+18%", "icon": "DollarSign", "color": "bg-yellow-500"}], "userGrowthData": [{"month": "Jan", "users": 1200, "id": "f4e3"}, {"month": "Feb", "users": 1450, "id": "5b04"}, {"month": "Mar", "users": 1680, "id": "f1bc"}, {"month": "Apr", "users": 1920, "id": "6700"}, {"month": "May", "users": 2340, "id": "c30f"}, {"month": "Jun", "users": 2847, "id": "c516"}], "progressData": [{"month": "Jan", "score": 65, "id": "33d9"}, {"month": "Feb", "score": 72, "id": "d128"}, {"month": "Mar", "score": 78, "id": "8812"}, {"month": "Apr", "score": 85, "id": "e9e7"}, {"month": "May", "score": 88, "id": "64bb"}, {"month": "Jun", "score": 92, "id": "6e40"}], "upcomingExams": [{"id": "1", "title": "UPSC Prelims Mock Test 16", "date": "March 20, 2024", "time": "10:00 AM"}, {"id": "2", "title": "Current Affairs Weekly Test", "date": "March 22, 2024", "time": "2:00 PM"}, {"id": "3", "title": "Indian History Practice Test", "date": "March 25, 2024", "time": "11:00 AM"}], "recentMaterials": [{"id": "1", "title": "Current Affairs March 2024", "type": "PDF", "price": "₹299"}, {"id": "2", "title": "Indian Polity Video Series", "type": "Video", "price": "₹599"}, {"id": "3", "title": "Economics Mind Maps", "type": "PDF", "price": "₹199"}], "userDailyQuizProgress": [{"id": "1", "userId": 1, "totalQuizzesTaken": 28, "averageScore": 78, "currentStreak": 7, "bestStreak": 12, "weeklyProgress": [{"date": "2024-03-14", "score": 80}, {"date": "2024-03-15", "score": 75}, {"date": "2024-03-16", "score": 85}, {"date": "2024-03-17", "score": 70}, {"date": "2024-03-18", "score": 90}, {"date": "2024-03-19", "score": 75}, {"date": "2024-03-20", "score": 80}]}, {"id": "2", "userId": "EeAgQRj4VhOIx6n2CvwTUcMM08V2", "totalQuizzesTaken": 15, "averageScore": 82, "currentStreak": 5, "bestStreak": 8, "weeklyProgress": [{"date": "2024-03-14", "score": 85}, {"date": "2024-03-15", "score": 78}, {"date": "2024-03-16", "score": 90}, {"date": "2024-03-17", "score": 75}, {"date": "2024-03-18", "score": 88}, {"date": "2024-03-19", "score": 82}, {"date": "2024-03-20", "score": 86}]}], "dailyQuizSubmissions": [{"id": "1", "userId": 1, "date": "2024-03-20", "score": 4, "totalQuestions": 5, "answers": [1, 2, 0, 1, 2], "timeSpent": 1200, "submittedAt": "2024-03-20T10:30:00Z"}], "downloadAttempts": [{"id": "dl_1753012399994_p2pj95099", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1753012324858", "downloadId": "dl_1753012399994_p2pj95099", "attemptedAt": "2025-07-20T11:53:19.994Z", "status": "completed", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "progress": 100}, {"id": "dl_1753012393102_a1o98cxbw", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1753012324858", "downloadId": "dl_1753012393102_a1o98cxbw", "attemptedAt": "2025-07-20T11:53:13.102Z", "status": "completed", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "progress": 100}, {"id": "dl_1753012384173_9s8jmkvla", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1753012324858", "downloadId": "dl_1753012384173_9s8jmkvla", "attemptedAt": "2025-07-20T11:53:04.174Z", "status": "completed", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "progress": 100}, {"id": "dl_1753012218846_ty914bqtf", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1752948467334", "downloadId": "dl_1753012218846_ty914bqtf", "attemptedAt": "2025-07-20T11:50:18.846Z", "status": "completed", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "progress": 100}, {"id": "dl_1753011888540_2oemasvb6", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1752948467334", "downloadId": "dl_1753011888540_2oemasvb6", "attemptedAt": "2025-07-20T11:44:48.540Z", "status": "completed", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "progress": 100}, {"id": "dl_1753011879071_eljgdwyvm", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1752948467334", "downloadId": "dl_1753011879071_eljgdwyvm", "attemptedAt": "2025-07-20T11:44:39.071Z", "status": "completed", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "progress": 100}, {"id": "dl_1753004114970_5zeuv20ev", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1753004025070", "downloadId": "dl_1753004114970_5zeuv20ev", "attemptedAt": "2025-07-20T09:35:14.971Z", "status": "completed", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "progress": 100}, {"id": "dl_1753004105534_031cw0xcn", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1753004025070", "downloadId": "dl_1753004105534_031cw0xcn", "attemptedAt": "2025-07-20T09:35:05.534Z", "status": "completed", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "progress": 100}, {"id": "dl_1753004089484_es020zubi", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1753004025070", "downloadId": "dl_1753004089484_es020zubi", "attemptedAt": "2025-07-20T09:34:49.484Z", "status": "completed", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "progress": 100}, {"id": "dl_1753003670667_krheqrrnx", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1752948467334", "downloadId": "dl_1753003670667_krheqrrnx", "attemptedAt": "2025-07-20T09:27:50.667Z", "status": "in_progress", "ipAddress": "**************", "userAgent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "progress": 90}, {"id": "dl_1750076646523_abc123", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1", "downloadId": "dl_1750076646523_abc123", "attemptedAt": "2025-06-16T12:24:06.523Z", "completedAt": "2025-06-16T12:24:21.523Z", "status": "completed", "ipAddress": "*************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "progress": 100, "fileSize": "15.2 MB", "downloadDuration": 15}, {"id": "dl_1752948467334_def456", "userId": "EeAgQRj4VhOIx6n2CvwTUcMM08V2", "materialId": "1752948467334", "downloadId": "dl_1752948467334_def456", "attemptedAt": "2025-07-19T18:30:00.000Z", "completedAt": "2025-07-19T18:30:12.000Z", "status": "completed", "ipAddress": "*************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "progress": 100, "fileSize": "4.1 MB", "downloadDuration": 12}, {"id": "dl_1752948679788_ghi789", "userId": "EeAgQRj4VhOIx6n2CvwTUcMM08V2", "materialId": "1752948679788", "downloadId": "dl_1752948679788_ghi789", "attemptedAt": "2025-07-19T19:15:00.000Z", "completedAt": null, "status": "failed", "ipAddress": "*************", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "progress": 45, "fileSize": "4.1 MB", "downloadDuration": 0}], "downloadLimits": [{"userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1", "maxDownloads": 5, "usedDownloads": 1, "lastDownloadAt": "2025-06-16T12:24:06.523Z", "resetDate": "2025-07-16T00:00:00Z"}, {"userId": "EeAgQRj4VhOIx6n2CvwTUcMM08V2", "materialId": "1752948467334", "maxDownloads": 3, "usedDownloads": 1, "lastDownloadAt": "2025-07-19T18:30:00.000Z", "resetDate": "2025-08-19T00:00:00Z"}, {"userId": "EeAgQRj4VhOIx6n2CvwTUcMM08V2", "materialId": "1752948679788", "maxDownloads": 3, "usedDownloads": 0, "lastDownloadAt": null, "resetDate": "2025-08-19T00:00:00Z"}, {"userId": "EeAgQRj4VhOIx6n2CvwTUcMM08V2", "materialId": "1752948886737", "maxDownloads": 5, "usedDownloads": 0, "lastDownloadAt": null, "resetDate": "2025-08-19T00:00:00Z"}, {"userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1753004025070", "maxDownloads": 5, "usedDownloads": 6, "lastDownloadAt": "2025-07-20T09:35:17.480Z", "resetDate": "2025-08-19T09:34:52.006Z"}, {"userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1752948467334", "maxDownloads": 5, "usedDownloads": 6, "lastDownloadAt": "2025-07-20T11:50:21.659Z", "resetDate": "2025-08-19T11:44:41.660Z"}, {"userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "materialId": "1753012324858", "maxDownloads": 5, "usedDownloads": 6, "lastDownloadAt": "2025-07-20T11:53:22.771Z", "resetDate": "2025-08-19T11:53:06.938Z"}], "examResults": [{"id": "1", "examId": "1", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "examTitle": "UPSC Prelims Mock Test 16", "score": 142, "totalQuestions": 100, "totalMarks": 200, "correctAnswers": 75, "incorrectAnswers": 20, "unanswered": 5, "timeTaken": "1h 45m", "rank": 245, "totalParticipants": 1250, "percentile": 82.5, "subjects": [{"name": "History", "score": 18, "total": 25, "marks": 36, "totalMarks": 50}, {"name": "Geography", "score": 20, "total": 25, "marks": 40, "totalMarks": 50}, {"name": "Polity", "score": 22, "total": 25, "marks": 44, "totalMarks": 50}, {"name": "Current Affairs", "score": 15, "total": 25, "marks": 30, "totalMarks": 50}], "completedAt": "2024-03-20T12:45:00Z", "submittedAt": "2024-03-20T12:45:00Z"}, {"id": "2", "examId": "2", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "examTitle": "Current Affairs Weekly Test", "score": 85, "totalQuestions": 50, "totalMarks": 100, "correctAnswers": 40, "incorrectAnswers": 5, "unanswered": 5, "timeTaken": "45m", "rank": 120, "totalParticipants": 890, "percentile": 90, "subjects": [{"name": "National Affairs", "score": 16, "total": 20, "marks": 32, "totalMarks": 40}, {"name": "International Affairs", "score": 12, "total": 15, "marks": 24, "totalMarks": 30}, {"name": "Economy", "score": 8, "total": 10, "marks": 16, "totalMarks": 20}, {"name": "Science & Technology", "score": 4, "total": 5, "marks": 8, "totalMarks": 10}], "completedAt": "2024-03-22T15:30:00Z", "submittedAt": "2024-03-22T15:30:00Z"}, {"id": "3", "examId": "3", "userId": "zFAaY9ANXzQHHCHpt40Jzoyc3nh1", "examTitle": "Indian History Practice Test", "score": 120, "totalQuestions": 75, "totalMarks": 150, "correctAnswers": 65, "incorrectAnswers": 8, "unanswered": 2, "timeTaken": "1h 20m", "rank": 180, "totalParticipants": 650, "percentile": 75, "subjects": [{"name": "Ancient History", "score": 22, "total": 25, "marks": 44, "totalMarks": 50}, {"name": "Medieval History", "score": 20, "total": 25, "marks": 40, "totalMarks": 50}, {"name": "Modern History", "score": 23, "total": 25, "marks": 46, "totalMarks": 50}], "completedAt": "2024-03-25T12:20:00Z", "submittedAt": "2024-03-25T12:20:00Z"}], "landingStats": [{"id": "1", "number": "50,000+", "label": "Active Students"}, {"id": "2", "number": "2,500+", "label": "Success Stories"}, {"id": "3", "number": "95%", "label": "Success Rate"}, {"id": "4", "number": "24/7", "label": "Support Available"}], "landingFeatures": [{"id": "1", "icon": "FileText", "title": "Weekly Mock Tests", "description": "UPSC-pattern tests with detailed analysis and performance insights", "color": "bg-blue-100 text-blue-600"}, {"id": "2", "icon": "BookOpen", "title": "Premium Study Materials", "description": "Curated content by top educators and subject matter experts", "color": "bg-green-100 text-green-600"}, {"id": "3", "icon": "TrendingUp", "title": "Progress Tracking", "description": "AI-powered analytics to track your preparation and improvement", "color": "bg-purple-100 text-purple-600"}, {"id": "4", "icon": "Users", "title": "Expert Mentorship", "description": "One-on-one guidance from successful IAS officers and educators", "color": "bg-orange-100 text-orange-600"}], "demoQuestions": [{"id": 1, "question": "Which of the following is the largest planet in our solar system?", "options": ["Earth", "Jupiter", "Saturn", "Neptune"], "correctAnswer": 1, "explanation": "Jupiter is the largest planet in our solar system with a mass greater than all other planets combined."}, {"id": 2, "question": "Who was the first Prime Minister of India?", "options": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dr. <PERSON>"], "correctAnswer": 1, "explanation": "<PERSON><PERSON><PERSON><PERSON> became India's first Prime Minister on August 15, 1947."}, {"id": 3, "question": "Which river is known as the 'Ganga of the South'?", "options": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "correctAnswer": 2, "explanation": "The Kaveri river is often called the 'Ganga of the South' due to its religious significance."}, {"id": 4, "question": "The Indian Constitution was adopted on which date?", "options": ["15th August 1947", "26th January 1950", "26th November 1949", "2nd October 1948"], "correctAnswer": 2, "explanation": "The Indian Constitution was adopted on November 26, 1949, and came into effect on January 26, 1950."}, {"id": 5, "question": "Which of the following is the largest planet in our solar system?", "options": ["Earth", "Jupiter", "Saturn", "Neptune"], "correctAnswer": 1, "explanation": "Jupiter is the largest planet in our solar system with a mass greater than all other planets combined."}, {"id": 6, "question": "Who was the first Prime Minister of India?", "options": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dr. <PERSON>"], "correctAnswer": 1, "explanation": "<PERSON><PERSON><PERSON><PERSON> became India's first Prime Minister on August 15, 1947."}, {"id": 7, "question": "Which river is known as the 'Ganga of the South'?", "options": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "correctAnswer": 2, "explanation": "The Kaveri river is often called the 'Ganga of the South' due to its religious significance."}, {"id": 8, "question": "The Indian Constitution was adopted on which date?", "options": ["15th August 1947", "26th January 1950", "26th November 1949", "2nd October 1948"], "correctAnswer": 2, "explanation": "The Indian Constitution was adopted on November 26, 1949, and came into effect on January 26, 1950."}, {"id": 9, "question": "Which of the following is the largest planet in our solar system?", "options": ["Earth", "Jupiter", "Saturn", "Neptune"], "correctAnswer": 1, "explanation": "Jupiter is the largest planet in our solar system with a mass greater than all other planets combined."}, {"id": 10, "question": "Who was the first Prime Minister of India?", "options": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dr. <PERSON>"], "correctAnswer": 1, "explanation": "<PERSON><PERSON><PERSON><PERSON> became India's first Prime Minister on August 15, 1947."}, {"id": 11, "question": "Which river is known as the 'Ganga of the South'?", "options": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "correctAnswer": 2, "explanation": "The Kaveri river is often called the 'Ganga of the South' due to its religious significance."}, {"id": 12, "question": "The Indian Constitution was adopted on which date?", "options": ["15th August 1947", "26th January 1950", "26th November 1949", "2nd October 1948"], "correctAnswer": 2, "explanation": "The Indian Constitution was adopted on November 26, 1949, and came into effect on January 26, 1950."}, {"id": 13, "question": "Which of the following is the largest planet in our solar system?", "options": ["Earth", "Jupiter", "Saturn", "Neptune"], "correctAnswer": 1, "explanation": "Jupiter is the largest planet in our solar system with a mass greater than all other planets combined."}, {"id": 14, "question": "Who was the first Prime Minister of India?", "options": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dr. <PERSON>"], "correctAnswer": 1, "explanation": "<PERSON><PERSON><PERSON><PERSON> became India's first Prime Minister on August 15, 1947."}, {"id": 15, "question": "Which river is known as the 'Ganga of the South'?", "options": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "correctAnswer": 2, "explanation": "The Kaveri river is often called the 'Ganga of the South' due to its religious significance."}, {"id": 16, "question": "The Indian Constitution was adopted on which date?", "options": ["15th August 1947", "26th January 1950", "26th November 1949", "2nd October 1948"], "correctAnswer": 2, "explanation": "The Indian Constitution was adopted on November 26, 1949, and came into effect on January 26, 1950."}], "newsItems": [{"id": "1", "title": "China's Dam Construction on Brahmaputra River", "category": "international", "date": "2025-01-15", "summary": "China formally begins dam construction on Brahmaputra River in Tibet, near Indian border in Arunachal Pradesh.", "details": "This development has significant implications for India's water security, bilateral relations with China, and environmental concerns in the northeastern region. The dam could affect water flow to India and Bangladesh, making it a critical issue for regional hydro-politics.", "relevance": "Important for International Relations, Environment & Ecology, and Internal Security sections. Links to India-China border disputes and water diplomacy.", "tags": ["Water Security", "India-China Relations", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Environmental Impact"], "importance": "high"}, {"id": "2", "title": "New Criminal Laws Implementation in India", "category": "governance", "date": "2025-01-10", "summary": "Several states implementing new criminal laws integrating technology for quicker justice delivery.", "details": "The new criminal laws represent a paradigm shift in India's legal framework, emphasizing technology integration, victim-centric approach, and faster case disposal. This includes digital evidence handling, online FIR registration, and AI-assisted case management.", "relevance": "Crucial for Polity & Governance, covering judicial reforms, technology in governance, and legal system modernization.", "tags": ["Criminal Law Reform", "Digital Justice", "Legal Technology", "Judicial System"], "importance": "high"}, {"id": "3", "title": "AI for Economic Transformation in India", "category": "technology", "date": "2025-01-08", "summary": "Analysis of Artificial Intelligence's role in transforming India's economy, innovation, and job markets.", "details": "AI is reshaping various sectors including healthcare, agriculture, manufacturing, and services. The government's AI mission focuses on responsible AI development, skill enhancement, and creating an AI-ready workforce while addressing concerns about job displacement.", "relevance": "Important for Science & Technology, Economy sections. Covers digital economy, future of work, and technological innovation.", "tags": ["Artificial Intelligence", "Digital Economy", "Innovation", "Future of Work"], "importance": "high"}, {"id": "4", "title": "Carbon Credit Trading Scheme and CBAM", "category": "environment", "date": "2025-01-05", "summary": "India's Carbon Credit Trading Scheme and EU's Carbon Border Adjustment Mechanism implications.", "details": "The Carbon Credit Trading Scheme aims to create a market-based mechanism for carbon reduction. CBAM will impact India's exports to EU, requiring carbon accounting and potentially affecting trade competitiveness in steel, cement, and aluminum sectors.", "relevance": "Critical for Environment & Ecology, International Trade, and Economy. Links to climate change mitigation and trade policy.", "tags": ["Carbon Trading", "Climate Policy", "International Trade", "Green Economy"], "importance": "high"}, {"id": "5", "title": "NATO Developments and Strategic Implications", "category": "international", "date": "2025-01-03", "summary": "Recent NATO developments and their strategic implications for India's foreign policy.", "details": "NATO's evolving role in global security, expansion discussions, and strategic partnerships affect India's multi-alignment policy. India's engagement with NATO partners while maintaining strategic autonomy remains crucial.", "relevance": "Important for International Relations, covering strategic partnerships, defense cooperation, and foreign policy.", "tags": ["NATO", "Strategic Partnership", "Defense Cooperation", "Foreign Policy"], "importance": "medium"}, {"id": "6", "title": "Valley of Flowers Conservation Spotlight", "category": "environment", "date": "2024-12-28", "summary": "Conservation efforts and biodiversity significance of Valley of Flowers National Park.", "details": "The UNESCO World Heritage site faces challenges from climate change and tourism pressure. Conservation efforts include habitat restoration, species monitoring, and sustainable tourism practices.", "relevance": "Relevant for Environment & Ecology, Geography, and Art & Culture sections. Covers biodiversity conservation and UNESCO sites.", "tags": ["Biodiversity", "UNESCO Heritage", "Conservation", "Uttarakhand"], "importance": "medium"}, {"id": "7", "title": "India's Electric Vehicle Transition Policy", "category": "economy", "date": "2024-12-25", "summary": "Policy debates and implementation strategies for electric vehicle adoption in India.", "details": "The EV transition involves infrastructure development, battery technology, charging networks, and policy incentives. Challenges include battery disposal, grid capacity, and affordability for mass adoption.", "relevance": "Important for Economy, Environment, and Science & Technology. Covers sustainable development and energy transition.", "tags": ["Electric Vehicles", "Sustainable Transport", "Energy Transition", "Green Mobility"], "importance": "high"}, {"id": "8", "title": "Tribal Empowerment and Demographic Dividend", "category": "governance", "date": "2024-12-20", "summary": "Policy initiatives for tribal empowerment and converting demographic trends into development opportunities.", "details": "Focus on tribal education, healthcare, livelihood opportunities, and preserving cultural identity while ensuring mainstream integration. Demographic dividend strategies include skill development and employment generation.", "relevance": "Crucial for Social Justice, Governance, and Indian Society sections. Covers inclusive development and social welfare.", "tags": ["Tribal Development", "Social Justice", "Demographic Dividend", "Inclusive Growth"], "importance": "medium"}], "recentActivities": [{"id": 1, "user": "<PERSON>", "action": "completed", "item": "Prelims Mock Test 15", "time": "2 hours ago", "timestamp": "2024-03-19T08:00:00.000Z"}, {"id": 2, "user": "<PERSON>", "action": "purchased", "item": "Current Affairs Package", "time": "3 hours ago", "timestamp": "2024-03-19T07:00:00.000Z"}, {"id": 3, "user": "Admin", "action": "created", "item": "New Geography Test", "time": "5 hours ago", "timestamp": "2024-03-19T05:00:00.000Z"}, {"id": 4, "user": "<PERSON>", "action": "joined", "item": "Premium Subscription", "time": "1 day ago", "timestamp": "2024-03-18T10:00:00.000Z"}, {"id": 5, "user": "<PERSON>", "action": "completed", "item": "History Mock Test 8", "time": "2 days ago", "timestamp": "2024-03-17T14:30:00.000Z"}]}