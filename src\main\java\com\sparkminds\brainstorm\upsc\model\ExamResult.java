package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date; // Changed from Object to Date
import java.util.List;

import static com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil.convertToLocalDateTime;

@Document(collectionName = "exam_results")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExamResult {
    @DocumentId
    private String id;
    private String examId;
    private String userId;
    private String examTitle;
    private Integer score;
    private Integer totalQuestions;
    private Integer totalMarks;
    private Integer correctAnswers;
    private Integer incorrectAnswers;
    private Integer unanswered;
    private String timeTakenMinutes;
    private Integer rank;
    private Integer totalParticipants;
    private Double percentile;
    private List<SubjectResult> subjects;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date completedAt; // Changed from Object to Date
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date submittedAt; // Changed from Object to Date

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubjectResult {
        private String name;
        private Integer score;
        private Integer total;
        private Integer marks;
        private Integer totalMarks;
    }

    public LocalDateTime getCompletedAtAsLocalDateTime() {
        return convertToLocalDateTime(completedAt); // Works with Date
    }

    public LocalDateTime getSubmittedAtAsLocalDateTime() {
        return convertToLocalDateTime(submittedAt); // Works with Date
    }
}