package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.dto.*;
import com.sparkminds.brainstorm.upsc.model.Exam;
import com.sparkminds.brainstorm.upsc.model.ExamResult;
import com.sparkminds.brainstorm.upsc.model.Question;
import com.sparkminds.brainstorm.upsc.model.User;
import com.sparkminds.brainstorm.upsc.repository.ExamRepository;
import com.sparkminds.brainstorm.upsc.repository.ExamResultRepository;
import com.sparkminds.brainstorm.upsc.repository.QuestionRepository;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ExamResultService {
    private static final Logger log = LoggerFactory.getLogger(ExamResultService.class);
    private final ExamResultRepository repository;
    private final QuestionRepository questionRepository;
    private final ExamRepository examRepository;
    private final UserService userService; // Added UserService dependency

    public ExamResultService(ExamResultRepository repository, QuestionRepository questionRepository, ExamRepository examRepository, UserService userService) {
        this.repository = repository;
        this.questionRepository = questionRepository;
        this.examRepository = examRepository;
        this.userService = userService;
    }

    public Flux<ExamResult> findAll() {
        return repository.findAll();
    }

    public Mono<ExamResult> findById(String id) {
        return repository.findById(id)
                .flatMap(result -> repository.findByExamId(result.getExamId())
                        .collectList()
                        .doOnNext(results -> calculateRanksAndPercentiles(results))
                        .flatMap(results -> Mono.just(result)));
    }

    public Mono<ExamResult> save(ExamResult obj) {
        if (obj.getSubmittedAt() == null) {
            obj.setSubmittedAt(new Date());
        }
        if (obj.getCompletedAt() == null) {
            obj.setCompletedAt(new Date());
        }
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }

    public Flux<ExamResult> findByUserId(String userId) {
        return repository.findByUserId(userId);
    }

    public Flux<ExamResult> findByExamId(String examId) {
        return repository.findByExamId(examId);
    }

    public Mono<ExamResultResponse> submitAndCalculateResult(SubmissionRequest request) {
        Mono<Exam> examMono = examRepository.findById(request.getExamId())
                .switchIfEmpty(Mono.error(new RuntimeException("Exam not found: " + request.getExamId())));

        Mono<Map<String, Question>> questionsMapMono = questionRepository.findByExamId(request.getExamId())
                .collect(Collectors.toMap(Question::getId, Function.identity()));

        return Mono.zip(examMono, questionsMapMono, SecurityUtils.getCurrentUserFirebaseUid().defaultIfEmpty("guest"))
                .flatMap(tuple -> {
                    Exam exam = tuple.getT1();
                    Map<String, Question> questionsMap = tuple.getT2();
                    String userId = tuple.getT3();
                    Map<String, String> userAnswers = request.getAnswers();

                    int correctAnswers = 0;
                    int incorrectAnswers = 0;
                    double totalMarks = 0;

                    for (Question question : questionsMap.values()) {
                        String userAnswer = userAnswers.get(question.getId());
                        if (userAnswer != null && !userAnswer.trim().isEmpty()) {
                            if (userAnswer.equals(question.getCorrectAnswer())) {
                                correctAnswers++;
                                totalMarks += question.getMarks();
                            } else {
                                incorrectAnswers++;
                                totalMarks -= question.getNegativeMarks();
                            }
                        }
                    }
                    int unanswered = questionsMap.size() - (correctAnswers + incorrectAnswers);

                    ExamResult result = new ExamResult();
                    result.setExamId(request.getExamId());
                    result.setUserId(userId);
                    result.setExamTitle(exam.getTitle());
                    result.setScore((int) Math.round(totalMarks));
                    result.setTotalQuestions(questionsMap.size());
                    result.setTotalMarks(exam.getTotalMarks());
                    result.setCorrectAnswers(correctAnswers);
                    result.setIncorrectAnswers(incorrectAnswers);
                    result.setUnanswered(unanswered);
                    result.setTimeTakenMinutes(request.getTimeTakenMinutes());
                    result.setSubmittedAt(new Date());
                    result.setCompletedAt(new Date());

                    return repository.save(result)
                            .flatMap(savedResult -> repository.findByExamId(request.getExamId())
                                    .collectList()
                                    .doOnNext(results -> calculateRanksAndPercentiles(results))
                                    .flatMap(results -> Mono.just(savedResult)));
                })
                .map(this::convertToResponse);
    }

    public Mono<ExamRankingResponse> getExamRanking(String examId) {
        return SecurityUtils.getCurrentUserFirebaseUid()
                .defaultIfEmpty("guest")
                .flatMap(currentUserId -> repository.findByExamId(examId)
                        .collectList()
                        .flatMap(results -> {
                            if (results.isEmpty()) {
                                return Mono.just(ExamRankingResponse.builder()
                                        .examId(examId)
                                        .examTitle("UPSC Prelims Mock Test " + examId)
                                        .totalParticipants(0)
                                        .topPerformers(Collections.emptyList())
                                        .statistics(new RankingStatistics())
                                        .lastUpdated(LocalDateTime.now().toString())
                                        .build());
                            }

                            // Sort results by correctAnswers (desc) and timeTakenMinutes (asc)
                            results.sort(
                                    Comparator.comparing(ExamResult::getCorrectAnswers, Comparator.nullsLast(Comparator.reverseOrder()))
                                            .thenComparing(result -> {
                                                try {
                                                    return Integer.parseInt(result.getTimeTakenMinutes());
                                                } catch (NumberFormatException e) {
                                                    return Integer.MAX_VALUE;
                                                }
                                            })
                            );

                            // Calculate ranks and percentiles
                            calculateRanksAndPercentiles(results);

                            // Get top 10 results
                            List<ExamResult> topResults = results.stream()
                                    .limit(10)
                                    .collect(Collectors.toList());

                            // Fetch user details for top results
                            return Flux.fromIterable(topResults)
                                    .flatMap(result -> userService.findById(result.getUserId())
                                            .map(user -> {
                                                String name = user.getEmail() != null ? user.getEmail() : "Unknown";
                                                if (result.getUserId().equals(currentUserId)) {
                                                    name = "you";
                                                }
                                                return RankingEntry.builder()
                                                        .rank(result.getRank())
                                                        .name(name)
                                                        .correctAnswers(result.getCorrectAnswers())
                                                        .build();
                                            })
                                            .defaultIfEmpty(RankingEntry.builder()
                                                    .rank(result.getRank())
                                                    .name(result.getUserId().equals(currentUserId) ? "you" : "Unknown")
                                                    .correctAnswers(result.getCorrectAnswers())
                                                    .build()))
                                    .collectList()
                                    .map(topPerformers -> {
                                        RankingStatistics statistics = calculateStatistics(results);
                                        return ExamRankingResponse.builder()
                                                .examId(examId)
                                                .examTitle("UPSC Prelims Mock Test " + examId)
                                                .totalParticipants(results.size())
                                                .topPerformers(topPerformers)
                                                .statistics(statistics)
                                                .lastUpdated(LocalDateTime.now().toString())
                                                .build();
                                    });
                        }));
    }

    public Mono<ExamResultResponse> submitExamResult(ExamResultRequest request) {
        ExamResult result = convertToEntity(request);
        return repository.save(result)
                .flatMap(savedResult -> repository.findByExamId(request.getExamId())
                        .collectList()
                        .doOnNext(results -> calculateRanksAndPercentiles(results))
                        .flatMap(results -> Mono.just(savedResult)))
                .map(this::convertToResponse);
    }

    private ExamResult convertToEntity(ExamResultRequest request) {
        ExamResult result = new ExamResult();
        result.setExamId(request.getExamId());
        result.setUserId(request.getUserId());
        result.setExamTitle(request.getExamTitle());
        result.setScore(request.getTotalMarks() != null ? request.getTotalMarks().intValue() : 0);
        result.setTotalQuestions(request.getTotalQuestions());
        result.setTotalMarks(request.getTotalMarks() != null ? request.getTotalMarks().intValue() : 0);
        result.setCorrectAnswers(request.getCorrectAnswers());
        result.setIncorrectAnswers(request.getIncorrectAnswers());
        result.setUnanswered(request.getUnanswered());
        result.setTimeTakenMinutes(request.getTimeTakenMinutes() != null ? request.getTimeTakenMinutes().toString() : "0");
        result.setSubjects(Collections.emptyList());
        result.setSubmittedAt(new Date());
        result.setCompletedAt(new Date());
        return result;
    }

    private void calculateRanksAndPercentiles(List<ExamResult> results) {
        int totalParticipants = results.size();
        for (int i = 0; i < results.size(); i++) {
            ExamResult result = results.get(i);
            result.setRank(i + 1);
            if (totalParticipants > 0) {
                result.setPercentile(((double) (totalParticipants - i) / totalParticipants) * 100);
            } else {
                result.setPercentile(0.0);
            }
        }
    }

    public RankingStatistics calculateStatistics(List<ExamResult> results) {
        if (results.isEmpty()) {
            return new RankingStatistics();
        }
        double avgScore = results.stream()
                .mapToDouble(r -> r.getScore() != null ? r.getScore() : 0.0)
                .average()
                .orElse(0.0);
        double avgTime = results.stream()
                .mapToInt(r -> {
                    try {
                        return Integer.parseInt(r.getTimeTakenMinutes());
                    } catch (Exception e) {
                        return 0;
                    }
                })
                .average()
                .orElse(0.0);
        double highestScore = results.stream()
                .mapToDouble(r -> r.getScore() != null ? r.getScore() : 0.0)
                .max()
                .orElse(0.0);
        double lowestScore = results.stream()
                .mapToDouble(r -> r.getScore() != null ? r.getScore() : 0.0)
                .min()
                .orElse(0.0);
        long passCount = results.stream()
                .filter(r -> r.getPercentile() != null && r.getPercentile() >= 60)
                .count();
        int passPercentage = (int) ((passCount * 100.0) / results.size());
        return RankingStatistics.builder()
                .averageScore(avgScore)
                .averageTime((int) avgTime)
                .highestScore(highestScore)
                .lowestScore(lowestScore)
                .passPercentage(passPercentage)
                .totalParticipants(results.size())
                .build();
    }

    private RankingEntry convertToRankingEntry(ExamResult result) {
        return RankingEntry.builder()
                .rank(result.getRank())
                .name(result.getUserId()) // Will be overridden in getExamRanking
                .correctAnswers(result.getCorrectAnswers())
                .build();
    }

    public ExamResultResponse convertToResponse(ExamResult result) {
        return ExamResultResponse.builder()
                .id(result.getId())
                .examId(result.getExamId())
                .userId(result.getUserId())
                .examTitle(result.getExamTitle())
                .score(result.getScore())
                .totalQuestions(result.getTotalQuestions())
                .totalMarks(result.getTotalMarks())
                .correctAnswers(result.getCorrectAnswers())
                .incorrectAnswers(result.getIncorrectAnswers())
                .unanswered(result.getUnanswered())
                .timeTakenMinutes(result.getTimeTakenMinutes())
                .rank(result.getRank())
                .percentile(result.getPercentile())
                .subjects(result.getSubjects())
                .completedAt(result.getCompletedAt() != null ? result.getCompletedAt().toString() : null)
                .submittedAt(result.getSubmittedAt() != null ? result.getSubmittedAt().toString() : null)
                .build();
    }
}