package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.dto.PurchaseSyncDTO;
import com.sparkminds.brainstorm.upsc.model.Purchase;
import com.sparkminds.brainstorm.upsc.repository.PurchaseRepository;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils; // Make sure this import exists
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class PaymentSyncService {

    private static final Logger log = LoggerFactory.getLogger(PaymentSyncService.class);

    private final WebClient paymentServiceWebClient;
    private final PurchaseRepository purchaseRepository;

    public PaymentSyncService(WebClient paymentServiceWebClient, PurchaseRepository purchaseRepository) {
        this.paymentServiceWebClient = paymentServiceWebClient;
        this.purchaseRepository = purchaseRepository;
    }

    /**
     * THIS IS THE NEW METHOD TO BE CALLED FROM YOUR CONTROLLER.
     * It syncs purchases by fetching data for both the parameter `userId` and the
     * `userId` from the security context, but assigns all new purchases to the parameter `userId`.
     *
     * @param parameterUserId The user ID passed from the frontend via request parameter. This is treated as the source of truth.
     * @return A Flux of newly created Purchase objects.
     */
    public Flux<Purchase> syncPurchasesByUserId(String parameterUserId) {
        // Get the userId from the security context. Use defaultIfEmpty for safety.
        Mono<String> contextUserIdMono = SecurityUtils.getCurrentUserFirebaseUid().defaultIfEmpty("");

        return contextUserIdMono.flatMapMany(contextUserId -> {
            log.info("Starting sync. Parameter UID: [{}], Context UID: [{}]", parameterUserId, contextUserId);

            // 1. Fetch purchases from the payment service for the PARAMETER userId.
            Flux<PurchaseSyncDTO> paramPurchases = fetchPurchasesFromPaymentService(parameterUserId);

            // 2. Fetch purchases for the CONTEXT userId ONLY if it's different and not empty.
            Flux<PurchaseSyncDTO> contextPurchases = Flux.empty();
            if (!contextUserId.isEmpty() && !contextUserId.equals(parameterUserId)) {
                log.info("Context UID is different, fetching its purchases as well.");
                contextPurchases = fetchPurchasesFromPaymentService(contextUserId);
            }

            // 3. Merge both streams and remove any duplicates by orderId.
            Flux<PurchaseSyncDTO> combinedRemotePurchases = Flux.merge(paramPurchases, contextPurchases)
                    .distinct(PurchaseSyncDTO::getOrderId);

            // 4. Fetch existing purchases from the local database for the PARAMETER userId.
            Mono<List<Purchase>> monolithPurchasesMono = purchaseRepository.findByUserId(parameterUserId).collectList();

            // 5. Zip the combined remote purchases and the local purchases together.
            return Mono.zip(combinedRemotePurchases.collectList(), monolithPurchasesMono)
                    .flatMapMany(tuple -> {
                        List<PurchaseSyncDTO> sourcePurchases = tuple.getT1();
                        List<Purchase> existingPurchases = tuple.getT2();

                        Set<String> existingOrderIds = existingPurchases.stream()
                                .map(Purchase::getOrderId)
                                .collect(Collectors.toSet());

                        log.info("Found a combined total of {} unique remote purchases and {} local purchases for user {}",
                                sourcePurchases.size(), existingPurchases.size(), parameterUserId);

                        List<PurchaseSyncDTO> newPurchasesToCreate = sourcePurchases.stream()
                                .filter(p -> p.getOrderId() != null && !existingOrderIds.contains(p.getOrderId()))
                                .collect(Collectors.toList());

                        if (newPurchasesToCreate.isEmpty()) {
                            log.info("No new purchases to sync for user {}", parameterUserId);
                            return Flux.empty();
                        }

                        log.info("Found {} new purchases to create for user {}", newPurchasesToCreate.size(), parameterUserId);

                        // 6. Create new Purchase objects and save them.
                        // CRITICAL: Pass the parameterUserId to ensure it's used for creation.
                        return Flux.fromIterable(newPurchasesToCreate)
                                .map(dto -> mapToPurchaseEntity(dto, parameterUserId))
                                .flatMap(purchaseRepository::save)
                                .doOnNext(savedPurchase -> log.info("Successfully synced and saved new purchase with orderId: {} for userId: {}", savedPurchase.getOrderId(), savedPurchase.getUserId()));
                    });
        });
    }

    private Flux<PurchaseSyncDTO> fetchPurchasesFromPaymentService(String userId) {
        log.debug("Fetching purchases from Payment Service for user: {}", userId);
        return paymentServiceWebClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/api/payments/sync-data")
                        .queryParam("userId", userId)
                        .build())
                .retrieve()
                .bodyToFlux(PurchaseSyncDTO.class)
                .doOnError(error -> log.error("Failed to fetch purchases from Payment Service for user {}: {}", userId, error.getMessage()))
                .onErrorResume(error -> Flux.empty());
    }

    /**
     * MODIFIED METHOD: Now accepts a second parameter to explicitly set the userId.
     *
     * @param dto The data transfer object from the payment service.
     * @param userIdToAssign The user ID that should be assigned to the new Purchase entity.
     * @return A new Purchase object ready to be saved.
     */
    private Purchase mapToPurchaseEntity(PurchaseSyncDTO dto, String userIdToAssign) {
        Purchase purchase = new Purchase();

        // CRITICAL CHANGE: Use the userId passed as a parameter, not the one from the DTO.
        purchase.setUserId(userIdToAssign);

        purchase.setMaterialId(dto.getMaterialId());
        purchase.setOrderId(dto.getOrderId());
        purchase.setPaymentId(dto.getPaymentId());
        purchase.setStatus(dto.getStatus());

        try {
            purchase.setPurchaseDate(LocalDateTime.parse(dto.getPurchaseDate(), DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        } catch (Exception e) {
            log.warn("Could not parse purchaseDate '{}', falling back to current time.", dto.getPurchaseDate());
            purchase.setPurchaseDate(LocalDateTime.now());
        }

        purchase.setCreatedAt(LocalDateTime.now());
        purchase.setUpdatedAt(LocalDateTime.now());
        purchase.setPaymentMethod("razorpay");
        purchase.setCurrency("INR");
        purchase.setAccessGranted(true);
        purchase.setDownloadCount(0);
        purchase.setMaxDownloads(5);
        purchase.setAmount(0.0);

        return purchase;
    }
}