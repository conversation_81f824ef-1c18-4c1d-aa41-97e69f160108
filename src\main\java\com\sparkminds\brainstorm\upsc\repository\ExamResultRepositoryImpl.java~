package com.sparkminds.brainstorm.upsc.repository;

import com.google.cloud.firestore.Firestore;
import com.google.cloud.spring.data.firestore.FirestoreTemplate;
import com.sparkminds.brainstorm.upsc.model.ExamResult;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Flux;

public class ExamResultRepositoryImpl implements ExamResultRepositoryCustom {

    private final FirestoreTemplate firestoreTemplate;
    private final Firestore firestore;

    @Autowired
    public ExamResultRepositoryImpl(FirestoreTemplate firestoreTemplate, Firestore firestore) {
        this.firestoreTemplate = firestoreTemplate;
        this.firestore = firestore;
    }

    @Override
    public Flux<ExamResult> findByParentId(String parentId) {
        return firestoreTemplate.findAll(
                ExamResult.class,
                firestore.collection("user_daily_quiz_progress").document(parentId).collection("exam_results")
        );
    }
}