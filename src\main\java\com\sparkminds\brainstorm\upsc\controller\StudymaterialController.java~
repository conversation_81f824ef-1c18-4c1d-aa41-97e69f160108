package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.repository.PurchaseRepository;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Studymaterial;
import com.sparkminds.brainstorm.upsc.model.DownloadAttempt;
import com.sparkminds.brainstorm.upsc.service.StudymaterialService;
import java.io.Serializable;
import java.util.Map;

@RestController
@RequestMapping("/api/studyMaterials")
public class StudymaterialController {
    private static final Logger log = LoggerFactory.getLogger(StudymaterialController.class);
    private final StudymaterialService service;
    private final PurchaseRepository purchaseRepository;

    public StudymaterialController(StudymaterialService service, PurchaseRepository purchaseRepository) {
        this.service = service;
        this.purchaseRepository = purchaseRepository;
    }

    @GetMapping
    public Flux<Studymaterial> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<Studymaterial> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @PostMapping
    public Mono<Studymaterial> create(@RequestBody Studymaterial obj) {
        if (obj.getIsPremium() != null && obj.getIsPremium() != 0 && obj.getIsPremium() != 1) {
            return Mono.error(new IllegalArgumentException("isPremium must be 0 or 1"));
        }
        return service.save(obj);
    }

    @PutMapping("/{id}")
    public Mono<Studymaterial> update(@PathVariable String id, @RequestBody Studymaterial obj) {
        if (obj.getIsPremium() != null && obj.getIsPremium() != 0 && obj.getIsPremium() != 1) {
            return Mono.error(new IllegalArgumentException("isPremium must be 0 or 1"));
        }
        obj.setId(id);
        return service.save(obj);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }

    @PostMapping("/upload")
    public Mono<Studymaterial> uploadMaterial(
            @RequestPart("file") Mono<FilePart> filePartMono,
            @RequestPart("title") Mono<String> titleMono,
            @RequestPart("description") Mono<String> descriptionMono,
            @RequestPart("subject") Mono<String> subjectMono,
            @RequestPart("price") Mono<String> priceMono,
            @RequestPart("author") Mono<String> authorMono,
            @RequestPart("tags") Mono<String> tagsMono,
            @RequestPart("isPremium") Mono<String> isPremiumMono
    ) {
        return Mono.zip(filePartMono, titleMono, descriptionMono, subjectMono,
                        priceMono, authorMono, tagsMono, isPremiumMono)
                .flatMap(tuple -> {
                    FilePart file = tuple.getT1();
                    String title = tuple.getT2();
                    String description = tuple.getT3();
                    String subject = tuple.getT4();
                    Integer price = Integer.parseInt(tuple.getT5());
                    String author = tuple.getT6();
                    String tags = tuple.getT7();
                    Integer isPremium = Integer.parseInt(tuple.getT8());
                    log.info("Uploading file: {}, title: {}", file.filename(), title);
                    return service.uploadMaterial(file, title, description, subject, price, author, tags, isPremium);
                });
    }

    @GetMapping("/download-limits/{userId}/{materialId}")
    public Mono<ResponseEntity<Map<String, Serializable>>> getDownloadLimits(
            @PathVariable String userId,
            @PathVariable String materialId) {
        return service.getDownloadLimits(userId, materialId)
                .map(limits -> ResponseEntity.ok(Map.of(
                        "maxDownloads", (Serializable) limits.getMaxDownloads(),
                        "usedDownloads", (Serializable) limits.getUsedDownloads(),
                        "remainingDownloads", (Serializable) (limits.getMaxDownloads() - limits.getUsedDownloads()),
                        "resetDate", (Serializable) limits.getResetDate()
                )))
                .defaultIfEmpty(ResponseEntity.ok(Map.of(
                        "maxDownloads", (Serializable) 5,
                        "usedDownloads", (Serializable) 0,
                        "remainingDownloads", (Serializable) 5
                )));
    }

    @GetMapping("/secure-download/{downloadId}")
    public Mono<ResponseEntity<Resource>> secureDownload(
            @PathVariable String downloadId,
            @RequestParam String token) {
        return service.validateDownloadToken(token)
                .flatMap(isValid -> {
                    if (!isValid) {
                        log.error("Invalid download token for downloadId: {}", downloadId);
                        return Mono.just(ResponseEntity.status(410).<Resource>build());
                    }
                    return service.findByDownloadId(downloadId)
                            .flatMap(material -> service.loadFileAsResource(material.getFilePath())
                                    .map(resource -> ResponseEntity.ok()
                                            .header(HttpHeaders.CONTENT_DISPOSITION,
                                                    "attachment; filename=\"" + material.getOriginalName() + "\"")
                                            .body(resource)))
                            .switchIfEmpty(Mono.just(ResponseEntity.notFound().build()));
                });
    }

    @PostMapping("/download-attempts")
    public Mono<ResponseEntity<Map<String, Boolean>>> recordDownloadAttempt(
            @RequestBody DownloadAttempt attempt) {
        return service.saveDownloadAttempt(attempt)
                .map(savedAttempt -> ResponseEntity.ok(Map.of("success", true)))
                .onErrorResume(error -> {
                    log.error("Failed to record download attempt: {}", error.getMessage());
                    return Mono.just(ResponseEntity.status(500).body(Map.of("success", false)));
                });
    }

    @PostMapping("/download-progress")
    public Mono<ResponseEntity<Map<String, Boolean>>> updateDownloadProgress(
            @RequestBody Map<String, Object> progressData) {
        String downloadId = (String) progressData.get("downloadId");
        Integer progress = (Integer) progressData.get("progress");
        String status = (String) progressData.get("status");
        return service.updateDownloadProgress(downloadId, progress, status)
                .map(updated -> ResponseEntity.ok(Map.of("success", true)))
                .onErrorResume(error -> {
                    log.error("Failed to update download progress: {}", error.getMessage());
                    return Mono.just(ResponseEntity.status(500).body(Map.of("success", false)));
                });
    }

    @PostMapping("/download-complete")
    public Mono<ResponseEntity<Map<String, Object>>> completeDownload(
            @RequestBody Map<String, String> request) {
        String downloadId = request.get("downloadId");
        return service.completeDownload(downloadId)
                .map(limitRecord -> ResponseEntity.ok(Map.<String, Object>of(
                        "success", true,
                        "limitRecord", Map.of(
                                "userId", limitRecord.getUserId(),
                                "materialId", limitRecord.getMaterialId(),
                                "maxDownloads", limitRecord.getMaxDownloads(),
                                "usedDownloads", limitRecord.getUsedDownloads(),
                                "lastDownloadAt", limitRecord.getLastDownloadAt(),
                                "resetDate", limitRecord.getResetDate()
                        )
                )))
                .onErrorResume(error -> {
                    log.error("Failed to complete download: {}", error.getMessage());
                    return Mono.just(ResponseEntity.status(500).body(Map.of("success", false)));
                });
    }

    @GetMapping("/{id}/download")
    public Mono<ResponseEntity<Resource>> downloadMaterial(
            @PathVariable String id,
            // CHANGE 1: Add userId as an optional request parameter
            @RequestParam(name = "userId", required = false) String paramUserId) {

        // CHANGE 2: Get the authenticated user ID from the security token
        return extractUserIdFromToken()
                .flatMap(contextUserId ->
                        // CHANGE 3: Pass BOTH user IDs to the service for the authorization check
                        service.canUserDownload(contextUserId, paramUserId, id, purchaseRepository)
                                .flatMap(canDownload -> {
                                    if (!canDownload) {
                                        log.error("User not authorized to download material {}. Checked contextUID: {} and paramUID: {}", id, contextUserId, paramUserId);
                                        return Mono.just(ResponseEntity.status(403).<Resource>body(null));
                                    }
                                    // If authorized, proceed with the download logic as before
                                    return service.findById(id)
                                            .flatMap(material -> service.loadFileAsResource(material.getFilePath())
                                                    .flatMap(resource -> service.incrementDownloadCount(id)
                                                            .map(updated -> {
                                                                String safeTitle = material.getTitle().replaceAll("[^a-zA-Z0-9-_]", "_");
                                                                String fileName = safeTitle + ".pdf";
                                                                return ResponseEntity.ok()
                                                                        .header(HttpHeaders.CONTENT_DISPOSITION,
                                                                                "attachment; filename=\"" + fileName + "\"")
                                                                        .contentType(MediaType.APPLICATION_PDF)
                                                                        .body(resource);
                                                            })))
                                            .switchIfEmpty(Mono.just(ResponseEntity.notFound().build()));
                                }))
                .onErrorResume(error -> {
                    log.error("Error downloading material {}: {}", id, error.getMessage());
                    return Mono.just(ResponseEntity.status(500)
                            .body(null));
                });
    }

    private Mono<String> extractUserIdFromToken(String authHeader) {
        return SecurityUtils.getCurrentUserFirebaseUid()
                .switchIfEmpty(Mono.error(new IllegalStateException("User not authenticated")))
                .doOnNext(firebaseUid -> log.info("Extracted Firebase UID: {}", firebaseUid))
                .doOnError(error -> log.error("Failed to extract Firebase UID: {}", error.getMessage()));
    }
}