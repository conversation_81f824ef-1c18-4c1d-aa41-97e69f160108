package com.sparkminds.brainstorm.upsc.controller;

import com.sparkminds.brainstorm.upsc.model.Purchase;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import com.sparkminds.brainstorm.upsc.service.PaymentSyncService;
import com.sparkminds.brainstorm.upsc.service.PurchaseService;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/api/purchases")
public class PurchaseController {
    private static final Logger log = LoggerFactory.getLogger(PurchaseController.class);
    private final PurchaseService service;
    private final PaymentSyncService paymentSyncService; // INJECT THE NEW SERVICE

    public PurchaseController(PurchaseService service, PaymentSyncService paymentSyncService) {
        this.service = service;
        this.paymentSyncService = paymentSyncService; // INITIALIZE IT
    }

    @GetMapping
    public Flux<Purchase> getAll() {
        return SecurityUtils.getCurrentUserFirebaseUid()
                .switchIfEmpty(Mono.error(new IllegalStateException("No authenticated user found")))
                .flatMapMany(userId -> {
                    log.info("Fetching purchases for authenticated userId: {}", userId);
                    return service.findByUserId(userId);
                });
    }

    @GetMapping("/{id}")
    public Mono<Purchase> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @GetMapping("/user/{userId}")
    public Flux<Purchase> getByUserId(@PathVariable String userId) {
        return service.findByUserId(userId);
    }

    @GetMapping("/material/{materialId}")
    public Mono<Purchase> getByMaterialId(@PathVariable String materialId) {
        return service.findByMatrialId(materialId);
    }

    @GetMapping("/access/{materialId}")
    public Mono<Purchase> getByUserIdAndMaterialId(@PathVariable String materialId) {
        return service.findByUserIdAndMaterialId(materialId);
    }

    @PostMapping
    public Mono<Purchase> create(@RequestBody Purchase obj) {
        log.info("Creating purchase for materialId: {}", obj.getMaterialId());
        if (obj.getMaterialId() == null || obj.getStatus() == null) {
            log.error("Invalid purchase data: materialId or status is missing: {}", obj);
            return Mono.error(new IllegalArgumentException("materialId and status are required"));
        }

        return SecurityUtils.getCurrentUserFirebaseUid()
                .switchIfEmpty(Mono.error(new IllegalStateException("No authenticated user found")))
                .flatMap(userId -> {
                    log.info("Authenticated user Firebase UID: {}", userId);
                    obj.setUserId(userId);
                    obj.setCreatedAt(LocalDateTime.now());
                    obj.setUpdatedAt(LocalDateTime.now());
                    obj.setAccessGranted(true);
                    obj.setDownloadCount(0);
                    obj.setMaxDownloads(5);
                    return service.save(obj)
                            .doOnSuccess(saved -> log.info("Purchase saved successfully: {}", saved))
                            .doOnError(error -> log.error("Failed to save purchase: {}", error.getMessage()));
                })
                .onErrorResume(e -> {
                    log.error("Error creating purchase: {}", e.getMessage());
                    return Mono.error(e);
                });
    }

    @PostMapping("/exam/{examId}")
    public Mono<ResponseEntity<Purchase>> purchaseExam(@PathVariable String examId, @RequestBody PurchaseRequest request) {
        return service.createExamPurchase(examId, request.getAmount(), request.getPaymentMethod(), request.getOrderId(), request.getPaymentId())
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.badRequest().build())
                .onErrorResume(e -> Mono.just(ResponseEntity.status(400).body(null)));
    }

    @PutMapping("/{id}")
    public Mono<Purchase> update(@PathVariable String id, @RequestBody Purchase obj) {
        obj.setId(id);
        return service.save(obj);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }

    // ##### THIS ENDPOINT IS NOW UPDATED #####
    @GetMapping("/sync")
    public Flux<Purchase> syncUserPurchases(@RequestParam String userId) {
        log.info("Received request to sync purchases for userId from parameter: {}", userId);
        return paymentSyncService.syncPurchasesByUserId(userId);
    }

    @Data
    static class PurchaseRequest {
        private String userId;
        private Double amount;
        private String paymentMethod;
        private String orderId;
        private String paymentId;
    }
}