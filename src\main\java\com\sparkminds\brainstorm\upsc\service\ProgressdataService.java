package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Progressdata;
import com.sparkminds.brainstorm.upsc.repository.ProgressdataRepository;

@Service
public class ProgressdataService {
    private final ProgressdataRepository repository;
    
    public ProgressdataService(ProgressdataRepository repository) { 
        this.repository = repository; 
    }
    
    public Flux<Progressdata> findAll() { 
        return repository.findAll(); 
    }
    
    public Mono<Progressdata> findById(String id) { 
        return repository.findById(id); 
    }
    
    public Mono<Progressdata> save(Progressdata obj) { 
        return repository.save(obj); 
    }
    
    public Mono<Void> delete(String id) { 
        return repository.deleteById(id); 
    }
}