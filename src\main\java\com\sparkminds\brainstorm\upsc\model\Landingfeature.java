package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Document(collectionName = "landing_features")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Landingfeature {
    @DocumentId
    private String id;
    private String icon;
    private String title;
    private String description;
    private String color;
}