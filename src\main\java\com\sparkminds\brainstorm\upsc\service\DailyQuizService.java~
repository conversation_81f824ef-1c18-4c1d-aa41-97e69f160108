package com.sparkminds.brainstorm.upsc.service;

import com.google.cloud.firestore.Firestore;
import com.sparkminds.brainstorm.upsc.model.DailyQuiz;
import com.sparkminds.brainstorm.upsc.model.Question;
import com.sparkminds.brainstorm.upsc.repository.DailyQuizRepository;
import com.sparkminds.brainstorm.upsc.repository.QuestionRepository;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Date;

@Service
public class DailyQuizService {
    private static final Logger logger = LoggerFactory.getLogger(DailyQuizService.class);
    private static final int DAILY_QUIZ_QUESTION_COUNT = 10;

    private final DailyQuizRepository dailyQuizRepository;
    private final QuestionRepository questionRepository;
    private final Firestore firestore;

    public DailyQuizService(
            DailyQuizRepository dailyQuizRepository,
            QuestionRepository questionRepository,
            Firestore firestore) {
        this.dailyQuizRepository = dailyQuizRepository;
        this.questionRepository = questionRepository;
        this.firestore = firestore;
    }

    public Flux<Question> getQuestionsForDailyQuiz(LocalDate localDate) {
        return getOrCreateDailyQuiz(localDate)
                .flatMapMany(dailyQuiz -> {
                    if (dailyQuiz.getQuestionIds() == null || dailyQuiz.getQuestionIds().isEmpty()) {
                        logger.warn("Daily quiz {} for date {} has no questions.", dailyQuiz.getId(), localDate);
                        return Flux.empty();
                    }
                    return questionRepository.findAllById(dailyQuiz.getQuestionIds());
                });
    }

    private Mono<DailyQuiz> getOrCreateDailyQuiz(LocalDate localDate) {
        if (localDate == null) {
            return Mono.error(new IllegalArgumentException("Date cannot be null"));
        }

        Date startOfDay = FirestoreDateTimeUtil.convertToDate(localDate);
        Date startOfNextDay = FirestoreDateTimeUtil.convertToDate(localDate.plusDays(1));

        logger.info("Searching for quiz for date {} (between {} and {})", localDate, startOfDay, startOfNextDay);

        return dailyQuizRepository.findByDateGreaterThanEqualAndDateLessThan(startOfDay, startOfNextDay)
                .next()
                .switchIfEmpty(Mono.defer(() -> generateNewDailyQuizForDate(localDate)))
                .doOnSuccess(quiz -> {
                    if (quiz != null) {
                        // MODIFIED: Enhanced logging when an existing quiz is found
                        logger.info("Successfully retrieved EXISTING quiz. ID: {}, Question IDs: {}, for date: {}", quiz.getId(), quiz.getQuestionIds(), localDate);
                    }
                })
                .doOnError(error -> logger.error("Failed to retrieve or create quiz for date {}: {}", localDate, error.getMessage()));
    }

    private Mono<DailyQuiz> generateNewDailyQuizForDate(LocalDate localDate) {
        logger.info("No quiz found for date {}. GENERATING a new one.", localDate);
        return questionRepository.findRandomQuestionIds(DAILY_QUIZ_QUESTION_COUNT, firestore)
                .collectList()
                .flatMap(questionIds -> {
                    if (questionIds.size() < DAILY_QUIZ_QUESTION_COUNT) {
                        return Mono.error(new RuntimeException("Not enough questions in the database to generate a daily quiz."));
                    }
                    DailyQuiz newQuiz = new DailyQuiz();
                    newQuiz.setDate(FirestoreDateTimeUtil.convertToDate(localDate));
                    newQuiz.setQuestionIds(questionIds);
                    return dailyQuizRepository.save(newQuiz)
                            .doOnSuccess(savedQuiz -> {
                                // MODIFIED: Enhanced logging when a new quiz is generated
                                logger.info("New quiz GENERATED. ID: {}, Question IDs: {}, for date: {}", savedQuiz.getId(), savedQuiz.getQuestionIds(), localDate);
                            });
                });
    }
}