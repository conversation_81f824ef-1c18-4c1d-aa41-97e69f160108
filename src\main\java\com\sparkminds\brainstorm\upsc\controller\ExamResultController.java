package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.service.ExamResultService;
import com.sparkminds.brainstorm.upsc.dto.*;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Flux; // Kept all original imports
import com.sparkminds.brainstorm.upsc.model.ExamResult;


@RestController
@RequestMapping("/api/exam-results")
public class ExamResultController {
    private final ExamResultService service;

    public ExamResultController(ExamResultService service) {
        this.service = service;
    }

    // ########## START OF MINIMAL ADDITION ##########
    @PostMapping("/submit")
    public Mono<ResponseEntity<ExamResultResponse>> submitAndGetResult(@RequestBody SubmissionRequest request) {
        return service.submitAndCalculateResult(request)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.badRequest().build());
    }
    // ########## END OF MINIMAL ADDITION ##########

    // All your original controller methods are preserved below
    @GetMapping
    public Flux<ExamResult> getAll() {
        return service.findAll();
    }

    @GetMapping("/{resultId}")
    public Mono<ResponseEntity<ExamResultResponse>> getExamResult(@PathVariable String resultId) {
        return service.findById(resultId)
                .map(service::convertToResponse)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    @GetMapping("/user/{userId}")
    public Flux<ExamResult> getByUserId(@PathVariable String userId) {
        return service.findByUserId(userId);
    }

    @GetMapping("/exam/{examId}")
    public Flux<ExamResult> getByExamId(@PathVariable String examId) {
        return service.findByExamId(examId);
    }

    @PutMapping("/{id}")
    public Mono<ExamResult> update(@PathVariable String id, @RequestBody ExamResult examResult) {
        examResult.setId(id);
        return service.save(examResult);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }

    @GetMapping("/ranking/{examId}")
    public Mono<ResponseEntity<ExamRankingResponse>> getExamRanking(@PathVariable String examId) {
        return service.getExamRanking(examId)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    @GetMapping("/live-ranking/{examId}")
    public Mono<ResponseEntity<LiveRankingResponse>> getLiveRanking(@PathVariable String examId) {
        return Mono.just(ResponseEntity.ok(new LiveRankingResponse()));
    }

    @GetMapping("/{resultId}/ranking")
    public Mono<ResponseEntity<RankingStatistics>> getResultRanking(@PathVariable String resultId) {
        return service.findById(resultId)
                .flatMap(result -> service.findByExamId(result.getExamId()).collectList())
                .map(service::calculateStatistics)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    // The old submission endpoint. Can be deprecated/removed later.
    @PostMapping
    public Mono<ResponseEntity<ExamResultResponse>> submitExamResult(@RequestBody ExamResultRequest request) {
        return service.submitExamResult(request)
                .map(ResponseEntity::ok)
                .defaultIfEmpty(ResponseEntity.badRequest().build());
    }
}