package com.sparkminds.brainstorm.upsc.service;

import com.sparkminds.brainstorm.upsc.dto.DashboardResponse;
import com.sparkminds.brainstorm.upsc.model.Dashboard;
import com.sparkminds.brainstorm.upsc.model.ExamResult;
import com.sparkminds.brainstorm.upsc.model.Userdailyquizprogre;
import com.sparkminds.brainstorm.upsc.repository.DashboardRepository;
import com.sparkminds.brainstorm.upsc.repository.ExamResultRepository;
import com.sparkminds.brainstorm.upsc.repository.UserdailyquizprogreRepository;
import com.sparkminds.brainstorm.upsc.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class DashboardService {
    private static final Logger logger = LoggerFactory.getLogger(DashboardService.class);
    private final DashboardRepository dashboardRepository;
    private final UserdailyquizprogreRepository quizProgressRepository;
    private final ExamResultRepository examResultRepository;

    public DashboardService(DashboardRepository dashboardRepository,
                            UserdailyquizprogreRepository quizProgressRepository,
                            ExamResultRepository examResultRepository) {
        this.dashboardRepository = dashboardRepository;
        this.quizProgressRepository = quizProgressRepository;
        this.examResultRepository = examResultRepository;
    }

    public Mono<DashboardResponse> getOrUpdateDashboard() {
        return SecurityUtils.getCurrentUserFirebaseUid()
                .defaultIfEmpty("guest")
                .flatMap(userId -> dashboardRepository.findByUserId(userId)
                        .flatMap(dashboard -> {
                            // Return existing dashboard immediately and trigger update in background
                            Mono<DashboardResponse> response = Mono.just(convertToResponse(dashboard));
                            triggerBackgroundUpdate(userId).subscribe();
                            return response;
                        })
                        .switchIfEmpty(Mono.defer(() -> createAndSaveNewDashboard(userId))));
    }

    private Mono<DashboardResponse> createAndSaveNewDashboard(String userId) {
        logger.info("Creating new dashboard for user: {}", userId);
        return Mono.zip(
                quizProgressRepository.findByUserId(userId).defaultIfEmpty(new Userdailyquizprogre()),
                examResultRepository.findByUserId(userId).collectList().defaultIfEmpty(List.of())
        ).flatMap(tuple -> {
            Userdailyquizprogre quizProgress = tuple.getT1();
            List<ExamResult> examResults = tuple.getT2();

            Dashboard dashboard = new Dashboard();
            dashboard.setUserId(userId);
            dashboard.setTotalQuizzesTaken(quizProgress.getTotalQuizzesTaken() != null ? quizProgress.getTotalQuizzesTaken() : 0);
            dashboard.setAverageQuizScore(quizProgress.getAverageScore() != null ? quizProgress.getAverageScore() : 0.0);
            dashboard.setCurrentStreak(quizProgress.getCurrentStreak() != null ? quizProgress.getCurrentStreak() : 0);
            dashboard.setBestStreak(quizProgress.getBestStreak() != null ? quizProgress.getBestStreak() : 0);

            // Calculate exam statistics
            int totalExams = examResults.size();
            double averageExamScore = examResults.stream()
                    .mapToDouble(r -> r.getScore() != null ? r.getScore() : 0.0)
                    .average()
                    .orElse(0.0);
            int totalCorrect = examResults.stream()
                    .mapToInt(r -> r.getCorrectAnswers() != null ? r.getCorrectAnswers() : 0)
                    .sum();
            int totalIncorrect = examResults.stream()
                    .mapToInt(r -> r.getIncorrectAnswers() != null ? r.getIncorrectAnswers() : 0)
                    .sum();
            int totalUnanswered = examResults.stream()
                    .mapToInt(r -> r.getUnanswered() != null ? r.getUnanswered() : 0)
                    .sum();

            dashboard.setTotalExamsTaken(totalExams);
            dashboard.setAverageExamScore(averageExamScore);
            dashboard.setTotalCorrectAnswers(totalCorrect);
            dashboard.setTotalIncorrectAnswers(totalIncorrect);
            dashboard.setTotalUnanswered(totalUnanswered);
            dashboard.setLastUpdated(LocalDateTime.now());

            return dashboardRepository.save(dashboard)
                    .map(this::convertToResponse);
        });
    }

    private Mono<Dashboard> triggerBackgroundUpdate(String userId) {
        return Mono.zip(
                quizProgressRepository.findByUserId(userId).defaultIfEmpty(new Userdailyquizprogre()),
                examResultRepository.findByUserId(userId).collectList().defaultIfEmpty(List.of())
        ).flatMap(tuple -> dashboardRepository.findByUserId(userId)
                .flatMap(dashboard -> {
                    Userdailyquizprogre quizProgress = tuple.getT1();
                    List<ExamResult> examResults = tuple.getT2();

                    dashboard.setTotalQuizzesTaken(quizProgress.getTotalQuizzesTaken() != null ? quizProgress.getTotalQuizzesTaken() : 0);
                    dashboard.setAverageQuizScore(quizProgress.getAverageScore() != null ? quizProgress.getAverageScore() : 0.0);
                    dashboard.setCurrentStreak(quizProgress.getCurrentStreak() != null ? quizProgress.getCurrentStreak() : 0);
                    dashboard.setBestStreak(quizProgress.getBestStreak() != null ? quizProgress.getBestStreak() : 0);

                    int totalExams = examResults.size();
                    double averageExamScore = examResults.stream()
                            .mapToDouble(r -> r.getScore() != null ? r.getScore() : 0.0)
                            .average()
                            .orElse(0.0);
                    int totalCorrect = examResults.stream()
                            .mapToInt(r -> r.getCorrectAnswers() != null ? r.getCorrectAnswers() : 0)
                            .sum();
                    int totalIncorrect = examResults.stream()
                            .mapToInt(r -> r.getIncorrectAnswers() != null ? r.getIncorrectAnswers() : 0)
                            .sum();
                    int totalUnanswered = examResults.stream()
                            .mapToInt(r -> r.getUnanswered() != null ? r.getUnanswered() : 0)
                            .sum();

                    dashboard.setTotalExamsTaken(totalExams);
                    dashboard.setAverageExamScore(averageExamScore);
                    dashboard.setTotalCorrectAnswers(totalCorrect);
                    dashboard.setTotalIncorrectAnswers(totalIncorrect);
                    dashboard.setTotalUnanswered(totalUnanswered);
                    dashboard.setLastUpdated(LocalDateTime.now());

                    return dashboardRepository.save(dashboard);
                })
                .switchIfEmpty(Mono.defer(() -> createAndSaveNewDashboard(userId).map(this::convertToDashboard))));
    }

    private DashboardResponse convertToResponse(Dashboard dashboard) {
        return DashboardResponse.builder()
                .userId(dashboard.getUserId())
                .totalQuizzesTaken(dashboard.getTotalQuizzesTaken())
                .averageQuizScore(dashboard.getAverageQuizScore())
                .currentStreak(dashboard.getCurrentStreak())
                .bestStreak(dashboard.getBestStreak())
                .totalExamsTaken(dashboard.getTotalExamsTaken())
                .averageExamScore(dashboard.getAverageExamScore())
                .totalCorrectAnswers(dashboard.getTotalCorrectAnswers())
                .totalIncorrectAnswers(dashboard.getTotalIncorrectAnswers())
                .totalUnanswered(dashboard.getTotalUnanswered())
                .lastUpdated(dashboard.getLastUpdated())
                .build();
    }

    private Dashboard convertToDashboard(DashboardResponse response) {
        Dashboard dashboard = new Dashboard();
        dashboard.setUserId(response.getUserId());
        dashboard.setTotalQuizzesTaken(response.getTotalQuizzesTaken());
        dashboard.setAverageQuizScore(response.getAverageQuizScore());
        dashboard.setCurrentStreak(response.getCurrentStreak());
        dashboard.setBestStreak(response.getBestStreak());
        dashboard.setTotalExamsTaken(response.getTotalExamsTaken());
        dashboard.setAverageExamScore(response.getAverageExamScore());
        dashboard.setTotalCorrectAnswers(response.getTotalCorrectAnswers());
        dashboard.setTotalIncorrectAnswers(response.getTotalIncorrectAnswers());
        dashboard.setTotalUnanswered(response.getTotalUnanswered());
        dashboard.setLastUpdated(response.getLastUpdated());
        return dashboard;
    }
}