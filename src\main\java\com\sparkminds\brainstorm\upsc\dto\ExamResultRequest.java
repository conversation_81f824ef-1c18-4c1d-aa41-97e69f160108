package com.sparkminds.brainstorm.upsc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
// ExamResultRequest.java
public class ExamResultRequest {
    private String examId;
    private String userId;
    private String userName;
    private String userEmail;
    private String examTitle;
    private Integer totalQuestions;
    private Integer correctAnswers;
    private Integer incorrectAnswers;
    private Integer unanswered;
    private Double totalMarks;
    private Double maxMarks;
    private Integer percentage;
    private Integer timeTakenMinutes;
    private String timeTakenFormatted;
    private String completedAt;
    private List<DetailedAnswer> detailedAnswers;
    private List<Integer> flaggedQuestions;
    // getters and setters
}
