package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Recentmaterial;
import com.sparkminds.brainstorm.upsc.repository.RecentmaterialRepository;

@Service
public class RecentmaterialService {
    private final RecentmaterialRepository repository;
    
    public RecentmaterialService(RecentmaterialRepository repository) { 
        this.repository = repository; 
    }
    
    public Flux<Recentmaterial> findAll() { 
        return repository.findAll(); 
    }
    
    public Mono<Recentmaterial> findById(String id) { 
        return repository.findById(id); 
    }
    
    public Mono<Recentmaterial> save(Recentmaterial obj) { 
        return repository.save(obj); 
    }
    
    public Mono<Void> delete(String id) { 
        return repository.deleteById(id); 
    }
}