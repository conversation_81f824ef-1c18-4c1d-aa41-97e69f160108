package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Notification;
import com.sparkminds.brainstorm.upsc.repository.NotificationRepository;

@Service
public class NotificationService {
    private final NotificationRepository repository;
    
    public NotificationService(NotificationRepository repository) { 
        this.repository = repository; 
    }
    
    public Flux<Notification> findAll() { 
        return repository.findAll(); 
    }
    
    public Mono<Notification> findById(String id) { 
        return repository.findById(id); 
    }
    
    public Mono<Notification> save(Notification obj) { 
        return repository.save(obj); 
    }
    
    public Mono<Void> delete(String id) { 
        return repository.deleteById(id); 
    }
}