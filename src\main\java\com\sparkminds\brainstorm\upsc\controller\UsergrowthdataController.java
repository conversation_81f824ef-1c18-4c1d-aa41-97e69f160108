package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Usergrowthdata;
import com.sparkminds.brainstorm.upsc.service.UsergrowthdataService;

@RestController
@RequestMapping("/api/userGrowthData")
public class UsergrowthdataController {
    private final UsergrowthdataService service;
    
    public UsergrowthdataController(UsergrowthdataService service) { 
        this.service = service; 
    }
    
    @GetMapping
    public Flux<Usergrowthdata> getAll() { 
        return service.findAll(); 
    }
    
    @GetMapping("/{id}")
    public Mono<Usergrowthdata> getById(@PathVariable String id) { 
        return service.findById(id); 
    }
    
    @PostMapping
    public Mono<Usergrowthdata> create(@RequestBody Usergrowthdata obj) { 
        return service.save(obj); 
    }
    
    @PutMapping("/{id}")
    public Mono<Usergrowthdata> update(@PathVariable String id, @RequestBody Usergrowthdata obj) { 
        obj.setId(id); 
        return service.save(obj); 
    }
    
    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) { 
        return service.delete(id); 
    }
}