package com.sparkminds.brainstorm.upsc.model;

import com.google.cloud.firestore.annotation.DocumentId;
import com.google.cloud.spring.data.firestore.Document;
import com.sparkminds.brainstorm.upsc.util.FirestoreDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Document(collectionName = "purchases")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Purchase {
    @DocumentId
    private String id;
    private String userId;
    private String materialId;
    private String planId;
    private String orderId;
    private String paymentId;
    private Double amount;
    private String currency;
    private String paymentMethod; // razorpay, cashfree, upi
    private String status; // pending, completed, failed, refunded
    private Object purchaseDate;
    private Object expiryDate;
    private Object createdAt;
    private Object updatedAt;
    private Boolean accessGranted;
    private Integer downloadCount;
    private Integer maxDownloads;
    private String transactionDetails;

    public LocalDateTime getPurchaseDateAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(purchaseDate);
    }

    public LocalDateTime getExpiryDateAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(expiryDate);
    }

    public LocalDateTime getCreatedAtAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(createdAt);
    }

    public LocalDateTime getUpdatedAtAsLocalDateTime() {
        return FirestoreDateTimeUtil.convertToLocalDateTime(updatedAt);
    }
}
