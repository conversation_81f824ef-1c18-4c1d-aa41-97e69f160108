package com.sparkminds.brainstorm.upsc.controller;

import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import com.sparkminds.brainstorm.upsc.model.Demoquestion;
import com.sparkminds.brainstorm.upsc.service.DemoquestionService;

@RestController
@RequestMapping("/api/demoQuestions")
public class DemoquestionController {
    private final DemoquestionService service;

    public DemoquestionController(DemoquestionService service) {
        this.service = service;
    }

    @GetMapping
    public Flux<Demoquestion> getAll() {
        return service.findAll();
    }

    @GetMapping("/{id}")
    public Mono<Demoquestion> getById(@PathVariable String id) {
        return service.findById(id);
    }

    @PostMapping
    public Mono<Demoquestion> create(@RequestBody Demoquestion obj) {
        return service.save(obj);
    }

    @PutMapping("/{id}")
    public Mono<Demoquestion> update(@PathVariable String id, @RequestBody Demoquestion obj) {
        obj.setId(id);
        return service.save(obj);
    }

    @DeleteMapping("/{id}")
    public Mono<Void> delete(@PathVariable String id) {
        return service.delete(id);
    }
}