package com.sparkminds.brainstorm.upsc.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class FirestoreDateDeserializer extends JsonDeserializer<Date> {
    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonNode node = p.getCodec().readTree(p);
        if (node.isObject() && node.has("year") && node.has("monthValue") && node.has("dayOfMonth")) {
            int year = node.get("year").asInt();
            int month = node.get("monthValue").asInt();
            int day = node.get("dayOfMonth").asInt();
            int hour = node.has("hour") ? node.get("hour").asInt() : 0;
            int minute = node.has("minute") ? node.get("minute").asInt() : 0;
            int second = node.has("second") ? node.get("second").asInt() : 0;
            int nano = node.has("nano") ? node.get("nano").asInt() : 0;
            LocalDateTime ldt = LocalDateTime.of(year, month, day, hour, minute, second, nano);
            return Date.from(ldt.atZone(ZoneId.systemDefault()).toInstant());
        }
        // fallback: try to parse as timestamp or string
        if (node.isTextual()) {
            try {
                return Date.from(LocalDateTime.parse(node.asText()).atZone(ZoneId.systemDefault()).toInstant());
            } catch (Exception e) {
                // ignore
            }
        }
        // fallback: try to parse as epoch millis
        if (node.isNumber()) {
            return new Date(node.asLong());
        }
        // fallback: try to parse Firestore Timestamp map
        if (node.isObject() && node.has("_seconds")) {
            long seconds = node.get("_seconds").asLong();
            long nanos = node.has("_nanoseconds") ? node.get("_nanoseconds").asLong() : 0L;
            return new Date(seconds * 1000 + nanos / 1000000);
        }
        // log the node for debugging
        System.err.println("Could not deserialize Firestore date: " + node.toString());
        return null;
    }
}
