package com.sparkminds.brainstorm.upsc.service;

import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.sparkminds.brainstorm.upsc.model.Newsitem;
import com.sparkminds.brainstorm.upsc.repository.NewsitemRepository;

@Service
public class NewsitemService {
    private final NewsitemRepository repository;

    public NewsitemService(NewsitemRepository repository) {
        this.repository = repository;
    }

    public Flux<Newsitem> findAll() {
        return repository.findAll();
    }

    public Mono<Newsitem> findById(String id) {
        return repository.findById(id);
    }

    public Mono<Newsitem> save(Newsitem obj) {
        return repository.save(obj);
    }

    public Mono<Void> delete(String id) {
        return repository.deleteById(id);
    }

    public Flux<Newsitem> findByCategory(String category) {
        return repository.findByCategory(category);
    }

    public Flux<Newsitem> findByImportance(String importance) {
        return repository.findByImportance(importance);
    }
}